"""
数据库常量和枚举模块。

此模块包含领域模型中使用的所有数据库相关常量、
命名约定和枚举值。
"""

from enum import Enum
from typing import Any, Dict



# ================================
# 类型别名
# ================================

# JSON类型别名 - 使用通用JSON而不是PostgreSQL特定的JSONB
JSONType = Dict[str, Any]

# ================================
# 状态枚举
# ================================

class CommonStatus:
    """通用状态枚举。
    
    此枚举定义了可在不同领域模型中使用的标准状态值，
    以保持一致性。
    
    属性:
        ACTIVE: 实体激活且可操作
        INACTIVE: 实体非激活但未删除
        PENDING: 实体待审批或处理
        SUSPENDED: 实体临时暂停
        DELETED: 实体标记为已删除
        LOCKED: 实体被锁定无法修改
    """
    
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"
    DELETED = "deleted"
    LOCKED = "locked"


class SecurityStatus:
    """通用状态枚举。

    此枚举定义了可在不同领域模型中使用的标准状态值，
    以保持一致性。

    属性:
        ACTIVE: 实体激活且可操作
        INACTIVE: 实体非激活但未删除
        PENDING: 实体待审批或处理
        SUSPENDED: 实体临时暂停
        DELETED: 实体标记为已删除
        LOCKED: 实体被锁定无法修改
    """

    PENDING = "pending"
    INVESTIGATING = "investigating"
    RESOLVED = "resolved"
    FALSE_POSITIVE = "false_positive"


# ================================
# 字段长度常量
# ================================

class FieldLengths:
    """标准字段长度常量，用于保持数据库架构一致性。"""
    
    # ID字段
    ID_LENGTH = 64
    
    # 字符串字段
    SHORT_STRING = 100
    MEDIUM_STRING = 255
    LONG_STRING = 500
    
    # 编码/名称字段
    CODE_LENGTH = 100
    NAME_LENGTH = 255
    
    # 状态字段
    STATUS_LENGTH = 20
    
    # 邮箱字段
    EMAIL_LENGTH = 320  # RFC 5321 标准
    
    # 电话字段
    PHONE_LENGTH = 20

# ================================
# 默认值
# ================================

class DefaultValues:
    """常用字段的默认值。"""
    
    # JSON字段默认值
    EMPTY_JSON = {}
    
    # 状态默认值
    DEFAULT_STATUS = CommonStatus.PENDING
    ACTIVE_STATUS = CommonStatus.ACTIVE
    
    # 数值默认值
    DEFAULT_RETRY_COUNT = 0
    DEFAULT_FAILED_ATTEMPTS = 0

# ================================
# 数据库架构常量
# ================================

class SchemaConstants:
    """数据库架构相关常量。"""
    
    # PostgreSQL的UUID生成函数
    UUID_FUNCTION = "uuid_generate_v4()::varchar"
    
    # 时区感知的时间戳函数
    CURRENT_TIMESTAMP = "current_timestamp()"
    
    # 常用索引名称
    TENANT_INDEX_SUFFIX = "_tenant_idx"
    STATUS_INDEX_SUFFIX = "_status_idx"
    CREATED_AT_INDEX_SUFFIX = "_created_at_idx"
