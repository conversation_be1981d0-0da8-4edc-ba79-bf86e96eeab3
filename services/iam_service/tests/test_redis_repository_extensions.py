"""
Redis仓库扩展方法测试

测试新添加的Redis仓库方法
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.persistence.redis.client import RedisConnector


class TestRedisRepositoryExtensions:
    """Redis仓库扩展方法测试类"""
    
    @pytest.fixture
    def mock_redis_client(self):
        """模拟Redis客户端"""
        mock_client = AsyncMock()
        return mock_client
    
    @pytest.fixture
    def mock_redis_connector(self, mock_redis_client):
        """模拟Redis连接器"""
        mock_connector = MagicMock(spec=RedisConnector)
        mock_connector.get_client.return_value = mock_redis_client
        return mock_connector
    
    @pytest.fixture
    def redis_repo(self, mock_redis_connector):
        """Redis仓库实例"""
        return RedisRepository(
            redis_connector=mock_redis_connector,
            key_prefix="test"
        )
    
    # --- 基础操作测试 ---
    
    @pytest.mark.asyncio
    async def test_keys_method(self, redis_repo, mock_redis_client):
        """测试keys方法"""
        # 模拟返回值
        mock_redis_client.keys.return_value = [b"test:key1", b"test:key2"]
        
        # 调用方法
        result = await redis_repo.keys("key*")
        
        # 验证结果
        assert result == ["test:key1", "test:key2"]
        mock_redis_client.keys.assert_called_once_with("test:key*")
    
    @pytest.mark.asyncio
    async def test_expire_method(self, redis_repo, mock_redis_client):
        """测试expire方法"""
        mock_redis_client.expire.return_value = True
        
        result = await redis_repo.expire("test_key", 3600)
        
        assert result is True
        mock_redis_client.expire.assert_called_once_with("test:test_key", 3600)
    
    @pytest.mark.asyncio
    async def test_ttl_method(self, redis_repo, mock_redis_client):
        """测试ttl方法"""
        mock_redis_client.ttl.return_value = 1800
        
        result = await redis_repo.ttl("test_key")
        
        assert result == 1800
        mock_redis_client.ttl.assert_called_once_with("test:test_key")
    
    @pytest.mark.asyncio
    async def test_incr_method(self, redis_repo, mock_redis_client):
        """测试incr方法"""
        mock_redis_client.incr.return_value = 5
        
        result = await redis_repo.incr("counter", 2)
        
        assert result == 5
        mock_redis_client.incr.assert_called_once_with("test:counter", 2)
    
    @pytest.mark.asyncio
    async def test_decr_method(self, redis_repo, mock_redis_client):
        """测试decr方法"""
        mock_redis_client.decr.return_value = 3
        
        result = await redis_repo.decr("counter", 1)
        
        assert result == 3
        mock_redis_client.decr.assert_called_once_with("test:counter", 1)
    
    # --- 列表操作测试 ---
    
    @pytest.mark.asyncio
    async def test_rpush_method(self, redis_repo, mock_redis_client):
        """测试rpush方法"""
        mock_redis_client.rpush.return_value = 3
        
        result = await redis_repo.rpush("list_key", "value1", "value2")
        
        assert result == 3
        mock_redis_client.rpush.assert_called_once_with("test:list_key", "value1", "value2")
    
    @pytest.mark.asyncio
    async def test_lpop_method(self, redis_repo, mock_redis_client):
        """测试lpop方法"""
        mock_redis_client.lpop.return_value = b"value1"
        
        result = await redis_repo.lpop("list_key")
        
        assert result == "value1"
        mock_redis_client.lpop.assert_called_once_with("test:list_key")
    
    @pytest.mark.asyncio
    async def test_llen_method(self, redis_repo, mock_redis_client):
        """测试llen方法"""
        mock_redis_client.llen.return_value = 5
        
        result = await redis_repo.llen("list_key")
        
        assert result == 5
        mock_redis_client.llen.assert_called_once_with("test:list_key")
    
    @pytest.mark.asyncio
    async def test_ltrim_method(self, redis_repo, mock_redis_client):
        """测试ltrim方法"""
        mock_redis_client.ltrim.return_value = True
        
        result = await redis_repo.ltrim("list_key", 0, 99)
        
        assert result is True
        mock_redis_client.ltrim.assert_called_once_with("test:list_key", 0, 99)
    
    # --- 集合操作测试 ---
    
    @pytest.mark.asyncio
    async def test_sismember_method(self, redis_repo, mock_redis_client):
        """测试sismember方法"""
        mock_redis_client.sismember.return_value = True
        
        result = await redis_repo.sismember("set_key", "member1")
        
        assert result is True
        mock_redis_client.sismember.assert_called_once_with("test:set_key", "member1")
    
    @pytest.mark.asyncio
    async def test_scard_method(self, redis_repo, mock_redis_client):
        """测试scard方法"""
        mock_redis_client.scard.return_value = 10
        
        result = await redis_repo.scard("set_key")
        
        assert result == 10
        mock_redis_client.scard.assert_called_once_with("test:set_key")
    
    # --- 批量操作测试 ---
    
    @pytest.mark.asyncio
    async def test_mget_method(self, redis_repo, mock_redis_client):
        """测试mget方法"""
        mock_redis_client.mget.return_value = [b"value1", None, b"value3"]
        
        result = await redis_repo.mget(["key1", "key2", "key3"])
        
        assert result == ["value1", None, "value3"]
        mock_redis_client.mget.assert_called_once_with(["test:key1", "test:key2", "test:key3"])
    
    @pytest.mark.asyncio
    async def test_mset_method(self, redis_repo, mock_redis_client):
        """测试mset方法"""
        mock_redis_client.mset.return_value = True
        
        mapping = {"key1": "value1", "key2": "value2"}
        result = await redis_repo.mset(mapping)
        
        assert result is True
        expected_mapping = {"test:key1": "value1", "test:key2": "value2"}
        mock_redis_client.mset.assert_called_once_with(expected_mapping)
    
    @pytest.mark.asyncio
    async def test_delete_pattern_method(self, redis_repo, mock_redis_client):
        """测试delete_pattern方法"""
        # 模拟keys方法返回匹配的键
        mock_redis_client.keys.return_value = ["test:session:1", "test:session:2"]
        mock_redis_client.delete.return_value = 2
        
        result = await redis_repo.delete_pattern("session:*")
        
        assert result == 2
        mock_redis_client.keys.assert_called_once_with("test:session:*")
        mock_redis_client.delete.assert_called_once()
    
    # --- 连接和信息测试 ---
    
    @pytest.mark.asyncio
    async def test_ping_method(self, redis_repo, mock_redis_client):
        """测试ping方法"""
        mock_redis_client.ping.return_value = True
        
        result = await redis_repo.ping()
        
        assert result is True
        mock_redis_client.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_ping_method_with_pong_response(self, redis_repo, mock_redis_client):
        """测试ping方法返回PONG"""
        mock_redis_client.ping.return_value = b"PONG"
        
        result = await redis_repo.ping()
        
        assert result is True
        mock_redis_client.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_ping_method_with_exception(self, redis_repo, mock_redis_client):
        """测试ping方法异常处理"""
        mock_redis_client.ping.side_effect = Exception("Connection failed")
        
        result = await redis_repo.ping()
        
        assert result is False
        mock_redis_client.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_info_method(self, redis_repo, mock_redis_client):
        """测试info方法"""
        mock_info = {"redis_version": "6.2.0", "used_memory": "1024000"}
        mock_redis_client.info.return_value = mock_info
        
        result = await redis_repo.info("memory")
        
        assert result == mock_info
        mock_redis_client.info.assert_called_once_with("memory")
    
    # --- 事务操作测试 ---
    
    @pytest.mark.asyncio
    async def test_watch_method(self, redis_repo, mock_redis_client):
        """测试watch方法"""
        mock_redis_client.watch.return_value = True
        
        result = await redis_repo.watch("key1", "key2")
        
        assert result is True
        mock_redis_client.watch.assert_called_once_with("test:key1", "test:key2")
    
    @pytest.mark.asyncio
    async def test_unwatch_method(self, redis_repo, mock_redis_client):
        """测试unwatch方法"""
        mock_redis_client.unwatch.return_value = True
        
        result = await redis_repo.unwatch()
        
        assert result is True
        mock_redis_client.unwatch.assert_called_once()
    
    # --- 发布订阅测试 ---
    
    @pytest.mark.asyncio
    async def test_publish_method(self, redis_repo, mock_redis_client):
        """测试publish方法"""
        mock_redis_client.publish.return_value = 2  # 订阅者数量
        
        result = await redis_repo.publish("notifications", "Hello World")
        
        assert result == 2
        mock_redis_client.publish.assert_called_once_with("test:notifications", "Hello World")
    
    # --- 工具方法测试 ---
    
    @pytest.mark.asyncio
    async def test_clear_all_with_prefix(self, redis_repo, mock_redis_client):
        """测试clear_all方法（有前缀）"""
        # 模拟keys和delete_pattern方法
        mock_redis_client.keys.return_value = ["test:key1", "test:key2"]
        mock_redis_client.delete.return_value = 2
        
        result = await redis_repo.clear_all()
        
        assert result is True
        mock_redis_client.keys.assert_called_once_with("test:*")
    
    @pytest.mark.asyncio
    async def test_clear_all_without_prefix(self, mock_redis_connector):
        """测试clear_all方法（无前缀）"""
        repo = RedisRepository(redis_connector=mock_redis_connector, key_prefix="")
        
        result = await repo.clear_all()
        
        # 没有前缀时应该返回False，避免误删
        assert result is False
    
    # --- 边界条件测试 ---
    
    @pytest.mark.asyncio
    async def test_keys_empty_result(self, redis_repo, mock_redis_client):
        """测试keys方法返回空结果"""
        mock_redis_client.keys.return_value = []
        
        result = await redis_repo.keys("nonexistent:*")
        
        assert result == []
        mock_redis_client.keys.assert_called_once_with("test:nonexistent:*")
    
    @pytest.mark.asyncio
    async def test_llen_zero_result(self, redis_repo, mock_redis_client):
        """测试llen方法返回0"""
        mock_redis_client.llen.return_value = 0
        
        result = await redis_repo.llen("empty_list")
        
        assert result == 0
        mock_redis_client.llen.assert_called_once_with("test:empty_list")
    
    @pytest.mark.asyncio
    async def test_smembers_with_bytes(self, redis_repo, mock_redis_client):
        """测试smembers方法处理字节类型"""
        mock_redis_client.smembers.return_value = {b"member1", b"member2", "member3"}
        
        result = await redis_repo.smembers("set_key")
        
        assert result == {"member1", "member2", "member3"}
        mock_redis_client.smembers.assert_called_once_with("test:set_key")
