"""
第二阶段实现功能测试

测试用户管理模块、认证安全模块、外部服务集成等新实现的功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timedelta

from security.security_utils import SecurityUtils
from security.jwt_manager import <PERSON><PERSON><PERSON>ana<PERSON>
from security.session_manager import <PERSON><PERSON>ana<PERSON>, DeviceInfo
from external_services.email_service import EmailService
from external_services.sms_service import SMSService, MockSMSProvider
from external_services.verification_service import VerificationService, VerificationCodeScene


class TestSecurityUtils:
    """安全工具类测试"""

    def setup_method(self):
        """测试前置设置"""
        self.security_utils = SecurityUtils()

    def test_password_hashing(self):
        """测试密码加密和验证"""
        password = "TestPassword123!"
        
        # 测试密码加密
        hashed = self.security_utils.hash_password(password)
        assert hashed != password
        assert len(hashed) > 50  # bcrypt哈希长度
        
        # 测试密码验证
        assert self.security_utils.verify_password(password, hashed)
        assert not self.security_utils.verify_password("wrong_password", hashed)

    def test_password_strength_check(self):
        """测试密码强度检查"""
        # 强密码
        strong_password = "StrongPass123!"
        result = self.security_utils.check_password_strength(strong_password)
        assert result["strength"] == "强"
        assert result["score"] >= 4
        
        # 弱密码
        weak_password = "123456"
        result = self.security_utils.check_password_strength(weak_password)
        assert result["strength"] == "弱"
        assert len(result["suggestions"]) > 0

    def test_verification_code_generation(self):
        """测试验证码生成"""
        # 数字验证码
        code = self.security_utils.generate_verification_code(6)
        assert len(code) == 6
        assert code.isdigit()
        
        # 备用恢复码
        backup_code = self.security_utils.generate_backup_code(8)
        assert len(backup_code) == 8

    def test_data_masking(self):
        """测试数据脱敏"""
        # 邮箱脱敏
        email = "<EMAIL>"
        masked = self.security_utils.mask_email(email)
        assert "te****@example.com" == masked
        
        # 手机号脱敏
        phone = "13800138000"
        masked = self.security_utils.mask_phone(phone)
        assert "138****8000" == masked


class TestJWTManager:
    """JWT管理器测试"""

    def setup_method(self):
        """测试前置设置"""
        self.jwt_manager = JWTManager(
            secret_key="test_secret_key_for_testing_only",
            algorithm="HS256",
            access_token_expire_minutes=60,
            refresh_token_expire_days=7
        )

    def test_token_generation_and_verification(self):
        """测试令牌生成和验证"""
        # 生成令牌对
        token_pair = self.jwt_manager.generate_token_pair(
            user_id="user_123",
            tenant_id="tenant_123",
            session_id="session_123",
            username="testuser",
            email="<EMAIL>",
            roles=["USER"],
            permissions=["user:read"]
        )
        
        assert token_pair.access_token
        assert token_pair.refresh_token
        assert token_pair.token_type == "Bearer"
        assert token_pair.access_expires_in > 0
        
        # 验证访问令牌
        payload = self.jwt_manager.verify_token(token_pair.access_token, "access")
        assert payload.user_id == "user_123"
        assert payload.username == "testuser"
        assert "USER" in payload.roles

    def test_token_expiry_check(self):
        """测试令牌过期检查"""
        token_pair = self.jwt_manager.generate_token_pair(
            user_id="user_123",
            tenant_id="tenant_123",
            session_id="session_123",
            username="testuser",
            email="<EMAIL>"
        )
        
        # 检查令牌是否未过期
        assert not self.jwt_manager.is_token_expired(token_pair.access_token)
        
        # 获取剩余时间
        remaining = self.jwt_manager.get_token_remaining_time(token_pair.access_token)
        assert remaining.total_seconds() > 0


class TestSessionManager:
    """会话管理器测试"""

    def setup_method(self):
        """测试前置设置"""
        self.redis_repo = AsyncMock()
        self.session_manager = SessionManager(
            redis_repo=self.redis_repo,
            default_session_timeout_hours=8,
            max_sessions_per_user=5
        )

    @pytest.mark.asyncio
    async def test_session_creation(self):
        """测试会话创建"""
        device_info = DeviceInfo(
            device_name="iPhone 15",
            os="iOS 17.0",
            ip_address="*************",
            user_agent="Mozilla/5.0..."
        )
        
        # 模拟Redis操作
        self.redis_repo.set = AsyncMock()
        self.redis_repo.get = AsyncMock(return_value=[])
        
        session_info = await self.session_manager.create_session(
            user_id="user_123",
            tenant_id="tenant_123",
            device_info=device_info
        )
        
        assert session_info.user_id == "user_123"
        assert session_info.tenant_id == "tenant_123"
        assert session_info.is_active
        assert session_info.session_id.startswith("session_")

    @pytest.mark.asyncio
    async def test_session_validation(self):
        """测试会话验证"""
        # 模拟有效会话
        valid_session_data = {
            "session_id": "session_123",
            "user_id": "user_123",
            "tenant_id": "tenant_123",
            "device_info": {
                "device_name": "iPhone 15",
                "os": "iOS 17.0",
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0..."
            },
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(hours=8)).isoformat(),
            "is_active": True,
            "login_ip": "*************"
        }
        
        self.redis_repo.get = AsyncMock(return_value=valid_session_data)
        
        is_valid = await self.session_manager.validate_session("session_123")
        assert is_valid


class TestEmailService:
    """邮件服务测试"""

    def setup_method(self):
        """测试前置设置"""
        self.email_service = EmailService(
            smtp_server="localhost",
            smtp_port=587,
            username="<EMAIL>",
            password="test_password",
            use_tls=True
        )

    @pytest.mark.asyncio
    async def test_email_content_generation(self):
        """测试邮件内容生成"""
        # 这里主要测试邮件模板和内容生成逻辑
        # 实际发送需要真实的SMTP服务器
        
        # 测试激活邮件内容生成
        activation_token = "test_activation_token"
        username = "testuser"
        tenant_name = "Test Company"
        
        # 验证邮件模板包含必要信息
        from external_services.email_service import EmailTemplate
        content = EmailTemplate.USER_ACTIVATION.format(
            username=username,
            tenant_name=tenant_name,
            activation_url=f"https://example.com/activate?token={activation_token}"
        )
        
        assert username in content
        assert tenant_name in content
        assert activation_token in content


class TestSMSService:
    """短信服务测试"""

    def setup_method(self):
        """测试前置设置"""
        self.sms_provider = MockSMSProvider()
        self.sms_service = SMSService(
            provider=self.sms_provider,
            rate_limit_per_minute=10,
            rate_limit_per_hour=100
        )

    @pytest.mark.asyncio
    async def test_sms_sending(self):
        """测试短信发送"""
        result = await self.sms_service.send_verification_code(
            phone="13800138000",
            verification_code="123456",
            tenant_name="Test Company"
        )
        
        assert result["success"]
        assert result["provider"] == "mock"
        assert "message_id" in result
        
        # 检查发送历史
        sent_messages = self.sms_provider.get_sent_messages()
        assert len(sent_messages) == 1
        assert "123456" in sent_messages[0]["message"]

    def test_phone_masking(self):
        """测试手机号脱敏"""
        phone = "13800138000"
        masked = self.sms_service.mask_phone(phone)
        assert masked == "138****8000"


class TestVerificationService:
    """验证码服务测试"""

    def setup_method(self):
        """测试前置设置"""
        self.redis_repo = AsyncMock()
        self.email_service = AsyncMock()
        self.sms_service = AsyncMock()
        
        self.verification_service = VerificationService(
            redis_repo=self.redis_repo,
            email_service=self.email_service,
            sms_service=self.sms_service
        )

    @pytest.mark.asyncio
    async def test_sms_code_sending(self):
        """测试短信验证码发送"""
        # 模拟Redis操作
        self.redis_repo.get = AsyncMock(return_value=None)  # 无频率限制
        self.redis_repo.set = AsyncMock()
        
        # 模拟短信发送成功
        self.sms_service.send_verification_code = AsyncMock(return_value={
            "success": True,
            "message_id": "test_message_id"
        })
        
        result = await self.verification_service.send_sms_code(
            phone="13800138000",
            scene=VerificationCodeScene.USER_REGISTRATION,
            tenant_name="Test Company"
        )
        
        assert "code_id" in result
        assert result["target"] == "138****8000"  # 脱敏显示
        assert result["expire_seconds"] == 300

    @pytest.mark.asyncio
    async def test_code_verification(self):
        """测试验证码验证"""
        code_id = "test_code_id"
        verification_code = "123456"
        
        # 模拟存储的验证码信息
        stored_code_info = {
            "code_id": code_id,
            "code": verification_code,
            "type": "sms",
            "target": "13800138000",
            "scene": "user_registration",
            "attempts": 0,
            "verified": False
        }
        
        self.redis_repo.get = AsyncMock(return_value=stored_code_info)
        self.redis_repo.delete = AsyncMock()
        
        result = await self.verification_service.verify_code(
            code_id=code_id,
            verification_code=verification_code
        )
        
        assert result["verified"]
        assert result["code_id"] == code_id


# 集成测试
class TestIntegration:
    """集成测试"""

    @pytest.mark.asyncio
    async def test_user_registration_flow(self):
        """测试用户注册流程集成"""
        # 这里可以测试完整的用户注册流程
        # 包括密码加密、验证码发送、邮件通知等
        
        security_utils = SecurityUtils()
        
        # 1. 密码加密
        password = "UserPassword123!"
        hashed_password = security_utils.hash_password(password)
        assert security_utils.verify_password(password, hashed_password)
        
        # 2. 生成激活令牌
        activation_token = security_utils.generate_secure_token()
        assert len(activation_token) > 20
        
        # 3. 验证码生成
        verification_code = security_utils.generate_verification_code()
        assert len(verification_code) == 6
        assert verification_code.isdigit()

    @pytest.mark.asyncio
    async def test_login_flow(self):
        """测试登录流程集成"""
        # 测试完整的登录流程
        # 包括密码验证、JWT生成、会话创建等
        
        security_utils = SecurityUtils()
        jwt_manager = JWTManager("test_secret", "HS256", 60, 7)
        
        # 1. 密码验证
        password = "UserPassword123!"
        hashed_password = security_utils.hash_password(password)
        assert security_utils.verify_password(password, hashed_password)
        
        # 2. JWT令牌生成
        token_pair = jwt_manager.generate_token_pair(
            user_id="user_123",
            tenant_id="tenant_123",
            session_id="session_123",
            username="testuser",
            email="<EMAIL>"
        )
        
        assert token_pair.access_token
        assert token_pair.refresh_token
        
        # 3. 令牌验证
        payload = jwt_manager.verify_token(token_pair.access_token, "access")
        assert payload.user_id == "user_123"


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
