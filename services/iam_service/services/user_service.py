"""
用户服务

提供用户管理的业务逻辑实现
支持用户生命周期管理、信息维护、状态管理和安全功能
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Type
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError
)
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    User, Tenant, Role, Permission,
    UserRole, RolePermission,
    AuditLog, AuditLogBuilder
)
from security.security_utils import SecurityUtils


class UserService:
    """用户服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        role_model: Type[Role],
        permission_model: Type[Permission],
        user_role_model: Type[UserRole],
        role_permission_model: Type[RolePermission],
        audit_log_model: Type[AuditLog],
        security_utils: SecurityUtils
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 安全工具
        self.security_utils = security_utils

        # 安全工具
        self.security_utils = SecurityUtils()

        # 用户状态定义
        self.USER_STATUS = {
            "PENDING": "pending",      # 待激活
            "ACTIVE": "active",        # 活跃
            "INACTIVE": "inactive",    # 非活跃
            "LOCKED": "locked",        # 锁定
            "DELETED": "deleted"       # 已删除
        }

        # 状态转换规则
        self.STATUS_TRANSITIONS = {
            "pending": ["active", "locked", "deleted"],
            "active": ["locked", "inactive", "deleted"],
            "locked": ["active", "deleted"],
            "inactive": ["active", "deleted"],
            "deleted": []  # 已删除状态无法转换
        }

    async def create_user(
        self,
        tenant_id: str,
        username: str,
        email: str,
        password: str,
        phone: Optional[str] = None,
        nickname: Optional[str] = None,
        status: str = "pending",
        profile: Optional[Dict[str, Any]] = None,
        send_welcome_email: bool = True
    ) -> Dict[str, Any]:
        """
        创建用户

        管理员创建用户账户，包含完整的初始化流程
        """
        try:
            # 验证租户是否存在且处于活跃状态
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            # 验证用户名、邮箱、手机号在租户内的唯一性
            await self._validate_user_uniqueness(tenant_id, username, email, phone)

            # 验证密码是否符合租户密码策略
            await self._validate_password_policy(tenant_id, password)

            # 生成用户ID
            user_id = f"user_{uuid.uuid4()}"

            # 加密密码
            encrypted_password = self.security_utils.hash_password(password)

            # 创建用户记录
            user = self.user_model(
                user_id=user_id,
                tenant_id=tenant_id,
                username=username,
                email=email,
                phone=phone,
                nickname=nickname,
                password_hash=encrypted_password,
                status=status,
                profile=profile or {},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                login_count=0
            )
            self.session.add(user)
            await self.session.flush()  # 获取生成的ID

            # 分配默认角色
            await self._assign_default_roles(user_id, tenant_id)

            # 生成激活令牌（如果状态为pending）
            activation_token = None
            if status == "pending":
                activation_token = await self._generate_activation_token(user_id)

            # 发送欢迎邮件（如果需要）
            if send_welcome_email:
                await self._send_welcome_email(user_id, email, username, activation_token)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # 创建者ID
                action="CREATE_USER",
                resource_type="USER",
                resource_id=user_id,
                details={"username": username, "email": email}
            )

            await self.session.commit()

            # 缓存用户基本信息
            await self._cache_user_info(user_id, {
                "user_id": user_id,
                "username": username,
                "email": email,
                "status": status,
                "tenant_id": tenant_id
            })

            # 返回创建结果
            return {
                "user_id": user_id,
                "username": username,
                "email": email,
                "phone": phone,
                "nickname": nickname,
                "status": status,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat(),
                "last_login": None,
                "login_count": 0,
                "roles": [],
                "profile": profile,
                "activation_required": status == "pending"
            }

        except IntegrityError as e:
            await self.session.rollback()
            if "username" in str(e):
                raise DuplicateResourceError("用户名已存在")
            elif "email" in str(e):
                raise DuplicateResourceError("邮箱已存在")
            elif "phone" in str(e):
                raise DuplicateResourceError("手机号已存在")
            else:
                raise DatabaseError("数据库约束错误")
        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建用户失败: {str(e)}")

    async def register_user(
        self,
        tenant_code: str,
        username: str,
        email: str,
        password: str,
        verification_code: str,
        code_id: str,
        phone: Optional[str] = None,
        nickname: Optional[str] = None,
        agree_terms: bool = True
    ) -> Dict[str, Any]:
        """
        用户注册

        用户自主注册功能，需要验证码验证
        """
        try:
            # TODO: 验证服务条款同意
            if not agree_terms:
                raise ValidationError("必须同意服务条款")

            # TODO: 根据租户编码获取租户信息
            # tenant = await self._get_tenant_by_code(tenant_code)
            # if not tenant or tenant.status != CommonStatus.ACTIVE:
            #     raise NotFoundError("租户不存在或未开放注册")

            # TODO: 验证验证码
            # await self._verify_code(code_id, verification_code, "register")

            # TODO: 验证用户名、邮箱、手机号在租户内的唯一性
            # await self._validate_user_uniqueness(tenant.tenant_id, username, email, phone)

            # 生成用户ID和激活令牌
            user_id = f"user_{uuid.uuid4()}"
            activation_token = f"activate_{uuid.uuid4()}"

            # TODO: 创建用户记录（待激活状态）
            # TODO: 存储激活令牌到Redis
            # TODO: 发送激活邮件
            # TODO: 记录审计日志

            return {
                "user_id": user_id,
                "username": username,
                "status": "pending",
                "activation_token": activation_token
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"用户注册失败: {str(e)}")

    async def activate_user(self, activation_token: str) -> Dict[str, Any]:
        """
        激活用户账户

        通过激活令牌激活用户账户
        """
        try:
            # TODO: 验证激活令牌
            # user_id = await self._verify_activation_token(activation_token)
            # if not user_id:
            #     raise ValidationError("激活令牌无效或已过期")

            # TODO: 获取用户信息
            # user = await self._get_user_by_id(user_id)
            # if not user:
            #     raise NotFoundError("用户不存在")

            # TODO: 检查用户状态
            # if user.status != "pending":
            #     raise BusinessError("用户已激活或状态异常")

            # TODO: 更新用户状态为活跃
            # user.status = "active"
            # user.activated_at = datetime.utcnow()
            # await self.session.commit()

            # TODO: 删除激活令牌
            # await self._delete_activation_token(activation_token)

            # TODO: 记录审计日志
            # await self._create_audit_log(
            #     tenant_id=user.tenant_id,
            #     user_id=user_id,
            #     action="ACTIVATE_USER",
            #     resource_type="USER",
            #     resource_id=user_id
            # )

            user_id = f"user_{uuid.uuid4()}"  # 临时返回
            return {
                "user_id": user_id,
                "status": "active",
                "activated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"激活用户失败: {str(e)}")

    async def list_users(
        self,
        tenant_id: str,
        cursor: Optional[str] = None,
        limit: int = 20,
        keyword: Optional[str] = None,
        status: Optional[str] = None,
        role_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询用户列表

        支持分页、搜索和筛选的用户列表查询
        """
        try:
            # TODO: 构建查询条件
            # TODO: 执行分页查询
            # TODO: 获取用户角色信息
            # TODO: 格式化返回数据

            # 临时返回模拟数据
            return {
                "items": [
                    {
                        "user_id": f"user_{uuid.uuid4()}",
                        "username": "john_doe",
                        "email": "<EMAIL>",
                        "phone": "***********",
                        "nickname": "约翰",
                        "status": "active",
                        "created_at": datetime.utcnow().isoformat(),
                        "updated_at": None,
                        "last_login": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                        "login_count": 25,
                        "roles": [
                            {
                                "role_id": f"role_{uuid.uuid4()}",
                                "role_name": "普通用户",
                                "role_code": "USER",
                                "assigned_at": datetime.utcnow().isoformat()
                            }
                        ],
                        "profile": {
                            "department": "技术部",
                            "position": "工程师"
                        }
                    }
                ],
                "total": 1,
                "next_cursor": None,
                "has_more": False
            }

        except Exception as e:
            raise BusinessError(f"查询用户列表失败: {str(e)}")

    async def get_user_detail(
        self,
        tenant_id: str,
        user_id: str,
        include_permissions: bool = False,
        include_sessions: bool = False
    ) -> Dict[str, Any]:
        """
        获取用户详情

        根据用户ID获取完整的用户详细信息
        """
        try:
            # TODO: 实现用户详情查询逻辑
            # 1. 验证用户存在性和租户权限
            # 2. 查询用户基本信息
            # 3. 获取用户角色和权限列表
            # 4. 查询用户个人资料
            # 5. 获取登录历史和统计
            # 6. 查询可访问的知识库
            # 7. 过滤敏感信息
            # 8. 缓存查询结果

            return {
                "user_id": user_id,
                "username": "demo_user",
                "email": "<EMAIL>",
                "phone": "***********",
                "nickname": "演示用户",
                "status": "active",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": None,
                "last_login": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                "login_count": 25,
                "failed_login_attempts": 0,
                "locked_until": None,
                "password_changed_at": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                "roles": [
                    {
                        "role_id": f"role_{uuid.uuid4()}",
                        "role_name": "普通用户",
                        "role_code": "USER",
                        "assigned_at": datetime.utcnow().isoformat()
                    }
                ],
                "profile": {
                    "avatar": "https://domain.com/avatars/demo.jpg",
                    "department": "研发部",
                    "position": "开发工程师"
                },
                "permissions": ["user:read", "document:read"] if include_permissions else [],
                "security_settings": {
                    "mfa_enabled": False,
                    "password_expires_at": (datetime.utcnow() + timedelta(days=90)).isoformat(),
                    "last_password_change": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                    "last_login": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                    "active_sessions": 2
                }
            }

        except Exception as e:
            raise BusinessError(f"获取用户详情失败: {str(e)}")

    async def get_user_profile(self, tenant_id: str, user_id: str) -> Dict[str, Any]:
        """
        获取用户个人资料

        获取用户的个人资料和偏好设置
        """
        try:
            # TODO: 实现用户资料查询逻辑
            return {
                "user_id": user_id,
                "username": "demo_user",
                "email": "<EMAIL>",
                "phone": "***********",
                "nickname": "演示用户",
                "profile": {
                    "avatar": "https://domain.com/avatars/demo.jpg",
                    "department": "研发部",
                    "position": "开发工程师",
                    "employee_id": "EMP001"
                },
                "preferences": {
                    "language": "zh-CN",
                    "timezone": "Asia/Shanghai",
                    "theme": "light",
                    "notifications": {
                        "email": True,
                        "sms": False,
                        "push": True
                    },
                    "dashboard_layout": "compact"
                },
                "security_info": {
                    "mfa_enabled": False,
                    "password_expires_at": (datetime.utcnow() + timedelta(days=90)).isoformat(),
                    "last_password_change": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                    "last_login": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                    "active_sessions": 2
                }
            }

        except Exception as e:
            raise BusinessError(f"获取用户个人资料失败: {str(e)}")

    async def update_user(
        self,
        tenant_id: str,
        user_id: str,
        nickname: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        status: Optional[str] = None,
        profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        更新用户信息

        支持部分字段更新的用户信息修改
        """
        try:
            # TODO: 实现用户更新逻辑
            # 1. 验证用户存在性和修改权限
            # 2. 检查新用户名和邮箱唯一性
            # 3. 验证状态变更的合法性
            # 4. 更新用户信息到数据库
            # 5. 处理状态变更的副作用
            # 6. 清理相关缓存
            # 7. 记录修改审计日志
            # 8. 发送状态变更通知

            return {
                "user_id": user_id,
                "username": "demo_user",
                "email": email or "<EMAIL>",
                "phone": phone,
                "nickname": nickname or "演示用户",
                "status": status or "active",
                "created_at": datetime.utcnow().isoformat(),
                "updated_at": datetime.utcnow().isoformat(),
                "last_login": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                "login_count": 25,
                "roles": [
                    {
                        "role_id": f"role_{uuid.uuid4()}",
                        "role_name": "普通用户",
                        "role_code": "USER",
                        "assigned_at": datetime.utcnow().isoformat()
                    }
                ],
                "profile": profile or {
                    "avatar": "https://domain.com/avatars/demo.jpg",
                    "department": "研发部",
                    "position": "开发工程师"
                }
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新用户信息失败: {str(e)}")

    async def update_user_preferences(
        self,
        tenant_id: str,
        user_id: str,
        preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新用户偏好设置

        更新用户的个人偏好和界面设置
        """
        try:
            # TODO: 实现用户偏好更新逻辑
            return {
                "user_id": user_id,
                "username": "demo_user",
                "email": "<EMAIL>",
                "phone": "***********",
                "nickname": "演示用户",
                "profile": {
                    "avatar": "https://domain.com/avatars/demo.jpg",
                    "department": "研发部",
                    "position": "开发工程师"
                },
                "preferences": preferences,
                "security_info": {
                    "mfa_enabled": False,
                    "password_expires_at": (datetime.utcnow() + timedelta(days=90)).isoformat(),
                    "last_password_change": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                    "last_login": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                    "active_sessions": 2
                }
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新用户偏好设置失败: {str(e)}")

    async def update_user_status(
        self,
        tenant_id: str,
        user_id: str,
        status: str,
        reason: Optional[str] = None,
        locked_until: Optional[str] = None,
        send_notification: bool = True
    ) -> Dict[str, Any]:
        """
        用户状态管理

        更新用户的状态，支持锁定、解锁、暂停等操作
        """
        try:
            # TODO: 实现用户状态更新逻辑
            # 1. 验证状态转换规则
            # 2. 更新用户状态
            # 3. 处理锁定时间
            # 4. 发送通知
            # 5. 记录审计日志

            return {
                "user_id": user_id,
                "old_status": "active",
                "new_status": status,
                "locked_until": locked_until,
                "updated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新用户状态失败: {str(e)}")

    async def delete_user(
        self,
        tenant_id: str,
        user_id: str,
        delete_type: str = "soft",
        reason: Optional[str] = None,
        transfer_data_to: Optional[str] = None,
        confirmation_code: str = None
    ) -> Dict[str, Any]:
        """
        删除用户

        支持软删除和硬删除的用户删除操作
        """
        try:
            # TODO: 实现用户删除逻辑
            # 1. 验证确认码
            # 2. 验证用户存在性和删除权限
            # 3. 检查用户依赖关系（创建的内容等）
            # 4. 执行软删除或硬删除
            # 5. 清理用户会话和令牌
            # 6. 移除角色和权限关联
            # 7. 处理用户数据归档或转移
            # 8. 清理所有相关缓存
            # 9. 记录删除审计日志

            return {
                "user_id": user_id,
                "status": "deleted",
                "deleted_at": datetime.utcnow().isoformat(),
                "data_transferred": transfer_data_to is not None,
                "sessions_terminated": 3
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"删除用户失败: {str(e)}")
    
    async def change_phone(
        self,
        tenant_id: str,
        user_id: str,
        old_phone: str,
        new_phone: str,
        sms_code: str,
        code_id: str
    ) -> Dict[str, Any]:
        """
        更换手机号

        需要短信验证码验证的手机号更换
        """
        try:
            # TODO: 实现手机号更换逻辑
            # 1. 验证短信验证码
            # 2. 检查新手机号唯一性
            # 3. 更新用户手机号
            # 4. 记录审计日志

            return {
                "user_id": user_id,
                "old_value": f"{old_phone[:3]}****{old_phone[-4:]}",
                "new_value": f"{new_phone[:3]}****{new_phone[-4:]}",
                "changed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更换手机号失败: {str(e)}")

    async def change_email(
        self,
        tenant_id: str,
        user_id: str,
        old_email: str,
        new_email: str,
        email_code: str,
        code_id: str
    ) -> Dict[str, Any]:
        """
        更换邮箱

        需要邮箱验证码验证的邮箱更换
        """
        try:
            # TODO: 实现邮箱更换逻辑
            # 1. 验证邮箱验证码
            # 2. 检查新邮箱唯一性
            # 3. 更新用户邮箱
            # 4. 记录审计日志

            return {
                "user_id": user_id,
                "old_value": f"{old_email.split('@')[0][:2]}****@{old_email.split('@')[1]}",
                "new_value": f"{new_email.split('@')[0][:2]}****@{new_email.split('@')[1]}",
                "changed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更换邮箱失败: {str(e)}")

    async def send_sms_code(
        self,
        tenant_id: str,
        phone: str,
        scene: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送短信验证码

        支持多种场景的短信验证码发送
        """
        try:
            # TODO: 实现短信验证码发送逻辑
            # 1. 验证发送频率限制
            # 2. 生成验证码
            # 3. 发送短信
            # 4. 存储验证码到Redis

            code_id = f"code_{uuid.uuid4()}"
            return {
                "target": f"{phone[:3]}****{phone[-4:]}",
                "code_id": code_id,
                "expire_seconds": 300,
                "sent_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"发送短信验证码失败: {str(e)}")

    async def send_email_code(
        self,
        tenant_id: str,
        email: str,
        scene: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送邮箱验证码

        支持多种场景的邮箱验证码发送
        """
        try:
            # TODO: 实现邮箱验证码发送逻辑
            # 1. 验证发送频率限制
            # 2. 生成验证码
            # 3. 发送邮件
            # 4. 存储验证码到Redis

            code_id = f"code_{uuid.uuid4()}"
            return {
                "target": f"{email.split('@')[0][:2]}****@{email.split('@')[1]}",
                "code_id": code_id,
                "expire_seconds": 300,
                "sent_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"发送邮箱验证码失败: {str(e)}")

    async def assign_roles(
        self,
        user_id: str,
        tenant_id: str,
        role_ids: List[str],
        assignment_type: str = "permanent",
        expires_at: Optional[str] = None,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        为用户分配角色

        支持永久分配和临时分配
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 验证角色存在性和有效性
            valid_roles = await self._validate_roles_exist(tenant_id, role_ids)

            # 检查用户是否已经拥有这些角色
            existing_roles = await self._get_user_existing_roles(user_id, role_ids)
            new_roles = [role_id for role_id in role_ids if role_id not in existing_roles]

            if not new_roles:
                return {
                    "user_id": user_id,
                    "assigned_roles": [],
                    "failed_roles": [{"role_id": role_id, "reason": "角色已分配"} for role_id in role_ids],
                    "assigned_at": datetime.utcnow().isoformat()
                }

            # 解析过期时间
            expires_dt = None
            if assignment_type == "temporary" and expires_at:
                expires_dt = datetime.fromisoformat(expires_at)
                if expires_dt <= datetime.utcnow():
                    raise ValidationError("过期时间不能早于当前时间")

            # 分配角色
            assigned_roles = []
            failed_roles = []

            for role_id in new_roles:
                try:
                    # 创建用户角色关联
                    user_role = self.user_role_model(
                        user_id=user_id,
                        role_id=role_id,
                        tenant_id=tenant_id,
                        assignment_type=assignment_type,
                        assigned_at=datetime.utcnow(),
                        expires_at=expires_dt,
                        assigned_by=None,  # TODO: 从上下文获取操作者ID
                        reason=reason
                    )
                    self.session.add(user_role)

                    # 获取角色信息
                    role_info = await self._get_role_info(role_id)
                    assigned_roles.append({
                        "role_id": role_id,
                        "role_name": role_info.get("name", ""),
                        "role_code": role_info.get("code", ""),
                        "assigned_at": datetime.utcnow().isoformat(),
                        "assignment_type": assignment_type,
                        "expires_at": expires_dt.isoformat() if expires_dt else None
                    })

                except Exception as e:
                    failed_roles.append({
                        "role_id": role_id,
                        "reason": f"分配失败: {str(e)}"
                    })

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="ASSIGN_USER_ROLES",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "role_ids": new_roles,
                    "assignment_type": assignment_type,
                    "expires_at": expires_at,
                    "reason": reason
                }
            )

            await self.session.commit()

            # 清除用户权限缓存
            await self._clear_user_permissions_cache(user_id)

            return {
                "user_id": user_id,
                "assigned_roles": assigned_roles,
                "failed_roles": failed_roles,
                "assigned_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"分配角色失败: {str(e)}")

    async def remove_roles(
        self,
        user_id: str,
        tenant_id: str,
        role_ids: List[str],
        reason: Optional[str] = None,
        force_remove: bool = False
    ) -> Dict[str, Any]:
        """
        移除用户角色

        支持依赖检查和强制移除
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 检查用户当前拥有的角色
            user_roles = await self._get_user_existing_roles(user_id, role_ids)
            roles_to_remove = [role_id for role_id in role_ids if role_id in user_roles]

            if not roles_to_remove:
                return {
                    "user_id": user_id,
                    "removed_roles": [],
                    "failed_roles": [{"role_id": role_id, "reason": "用户未拥有此角色"} for role_id in role_ids],
                    "remaining_roles": await self._get_user_all_roles(user_id),
                    "removed_at": datetime.utcnow().isoformat()
                }

            # 检查角色依赖关系（除非强制移除）
            if not force_remove:
                dependency_check = await self._check_role_dependencies(user_id, roles_to_remove)
                if dependency_check["has_dependencies"]:
                    raise ValidationError(f"存在角色依赖关系，无法移除: {dependency_check['dependencies']}")

            # 移除角色
            removed_roles = []
            failed_roles = []

            for role_id in roles_to_remove:
                try:
                    # 删除用户角色关联
                    stmt = select(self.user_role_model).where(
                        and_(
                            self.user_role_model.user_id == user_id,
                            self.user_role_model.role_id == role_id,
                            self.user_role_model.tenant_id == tenant_id
                        )
                    )
                    result = await self.session.execute(stmt)
                    user_role = result.scalar_one_or_none()

                    if user_role:
                        await self.session.delete(user_role)
                        removed_roles.append(role_id)
                    else:
                        failed_roles.append({
                            "role_id": role_id,
                            "reason": "角色关联不存在"
                        })

                except Exception as e:
                    failed_roles.append({
                        "role_id": role_id,
                        "reason": f"移除失败: {str(e)}"
                    })

            # 获取移除后剩余的角色
            remaining_roles = await self._get_user_all_roles(user_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="REMOVE_USER_ROLES",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "role_ids": removed_roles,
                    "reason": reason,
                    "force_remove": force_remove
                }
            )

            await self.session.commit()

            # 清除用户权限缓存
            await self._clear_user_permissions_cache(user_id)

            return {
                "user_id": user_id,
                "removed_roles": removed_roles,
                "failed_roles": failed_roles,
                "remaining_roles": remaining_roles,
                "removed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"移除角色失败: {str(e)}")

    async def get_user_permissions(
        self,
        user_id: str,
        tenant_id: str,
        resource_type: Optional[str] = None,
        include_inherited: bool = True
    ) -> Dict[str, Any]:
        """
        查询用户有效权限

        支持按资源类型筛选和权限继承
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 从缓存获取权限信息
            cache_key = f"user_permissions:{user_id}:{resource_type or 'all'}"
            cached_permissions = await self.redis_repo.get(cache_key)

            if cached_permissions:
                return cached_permissions

            # 查询用户直接权限和角色权限
            permissions = await self._get_user_effective_permissions(
                user_id, tenant_id, resource_type, include_inherited
            )

            # 缓存权限信息
            await self.redis_repo.set(cache_key, permissions, ttl=1800)  # 30分钟过期

            return permissions

        except Exception as e:
            raise BusinessError(f"查询用户权限失败: {str(e)}")

    async def get_user_permission_sources(
        self,
        user_id: str,
        tenant_id: str,
        permission_code: str
    ) -> Dict[str, Any]:
        """
        查询用户权限来源

        分析权限的具体来源（直接分配、角色继承等）
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            sources = await self._analyze_permission_sources(user_id, tenant_id, permission_code)

            return {
                "user_id": user_id,
                "permission_code": permission_code,
                "has_permission": len(sources) > 0,
                "sources": sources,
                "analyzed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"查询权限来源失败: {str(e)}")

    async def get_user_role_history(
        self,
        user_id: str,
        tenant_id: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        查询用户角色历史

        包括角色分配和移除的历史记录
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 解析时间范围
            start_dt = datetime.fromisoformat(start_date) if start_date else None
            end_dt = datetime.fromisoformat(end_date) if end_date else None

            # 查询角色历史
            history = await self._get_user_role_history(user_id, start_dt, end_dt, limit)

            return {
                "user_id": user_id,
                "total_records": len(history),
                "history": history,
                "queried_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"查询角色历史失败: {str(e)}")

    async def get_user_sessions(
        self,
        user_id: str,
        tenant_id: str,
        status: Optional[str] = None,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        查询用户活跃会话

        获取用户当前的活跃会话信息
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 从Redis查询会话信息
            sessions = await self._get_user_active_sessions(user_id, status, limit)

            return {
                "user_id": user_id,
                "total_sessions": len(sessions),
                "sessions": sessions,
                "queried_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"查询用户会话失败: {str(e)}")

    async def terminate_user_session(
        self,
        user_id: str,
        tenant_id: str,
        session_id: str,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        强制下线指定会话

        终止用户的特定会话
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 验证会话存在性
            session_info = await self._get_session_info(session_id)
            if not session_info or session_info.get("user_id") != user_id:
                raise NotFoundError("会话不存在或不属于该用户")

            # 终止会话
            await self._terminate_session(session_id, reason)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="TERMINATE_USER_SESSION",
                resource_type="SESSION",
                resource_id=session_id,
                details={
                    "target_user_id": user_id,
                    "reason": reason
                }
            )

            return {
                "user_id": user_id,
                "session_id": session_id,
                "status": "terminated",
                "terminated_at": datetime.utcnow().isoformat(),
                "reason": reason
            }

        except Exception as e:
            raise BusinessError(f"终止会话失败: {str(e)}")

    async def terminate_all_user_sessions(
        self,
        user_id: str,
        tenant_id: str,
        exclude_current: bool = True,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        强制下线所有会话

        终止用户的所有活跃会话
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 获取所有活跃会话
            sessions = await self._get_user_active_sessions(user_id, "active")

            # TODO: 如果exclude_current为True，需要排除当前会话
            # current_session_id = self._get_current_session_id()

            terminated_count = 0
            failed_sessions = []

            for session in sessions:
                try:
                    await self._terminate_session(session["session_id"], reason)
                    terminated_count += 1
                except Exception as e:
                    failed_sessions.append({
                        "session_id": session["session_id"],
                        "error": str(e)
                    })

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="TERMINATE_ALL_USER_SESSIONS",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "terminated_count": terminated_count,
                    "failed_count": len(failed_sessions),
                    "reason": reason
                }
            )

            return {
                "user_id": user_id,
                "total_sessions": len(sessions),
                "terminated_count": terminated_count,
                "failed_sessions": failed_sessions,
                "terminated_at": datetime.utcnow().isoformat(),
                "reason": reason
            }

        except Exception as e:
            raise BusinessError(f"终止所有会话失败: {str(e)}")

    async def batch_assign_roles(
        self,
        tenant_id: str,
        user_ids: List[str],
        role_ids: List[str],
        assignment_type: str = "permanent",
        expires_at: Optional[str] = None,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量角色分配

        为多个用户批量分配角色
        """
        try:
            # 验证角色存在性
            valid_roles = await self._validate_roles_exist(tenant_id, role_ids)

            # 批量处理结果
            successful_assignments = []
            failed_assignments = []

            for user_id in user_ids:
                try:
                    result = await self.assign_roles(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        role_ids=role_ids,
                        assignment_type=assignment_type,
                        expires_at=expires_at,
                        reason=reason
                    )
                    successful_assignments.append({
                        "user_id": user_id,
                        "assigned_roles": result["assigned_roles"]
                    })
                except Exception as e:
                    failed_assignments.append({
                        "user_id": user_id,
                        "error": str(e)
                    })

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="BATCH_ASSIGN_ROLES",
                resource_type="USER",
                resource_id="batch",
                details={
                    "user_ids": user_ids,
                    "role_ids": role_ids,
                    "successful_count": len(successful_assignments),
                    "failed_count": len(failed_assignments)
                }
            )

            return {
                "total_users": len(user_ids),
                "successful_count": len(successful_assignments),
                "failed_count": len(failed_assignments),
                "successful_assignments": successful_assignments,
                "failed_assignments": failed_assignments,
                "processed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"批量分配角色失败: {str(e)}")

    async def batch_update_status(
        self,
        tenant_id: str,
        user_ids: List[str],
        status: str,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量状态更新

        批量更新用户状态
        """
        try:
            # 验证状态有效性
            if status not in self.USER_STATUS.values():
                raise ValidationError(f"无效的用户状态: {status}")

            successful_updates = []
            failed_updates = []

            for user_id in user_ids:
                try:
                    result = await self.update_user_status(
                        tenant_id=tenant_id,
                        user_id=user_id,
                        status=status,
                        reason=reason
                    )
                    successful_updates.append({
                        "user_id": user_id,
                        "old_status": result.get("old_status"),
                        "new_status": status
                    })
                except Exception as e:
                    failed_updates.append({
                        "user_id": user_id,
                        "error": str(e)
                    })

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="BATCH_UPDATE_STATUS",
                resource_type="USER",
                resource_id="batch",
                details={
                    "user_ids": user_ids,
                    "status": status,
                    "successful_count": len(successful_updates),
                    "failed_count": len(failed_updates),
                    "reason": reason
                }
            )

            return {
                "total_users": len(user_ids),
                "successful_count": len(successful_updates),
                "failed_count": len(failed_updates),
                "successful_updates": successful_updates,
                "failed_updates": failed_updates,
                "processed_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"批量更新状态失败: {str(e)}")

    async def get_user_activity_statistics(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        用户活跃度统计

        统计用户的活跃度数据
        """
        try:
            # 解析时间范围
            start_dt = datetime.fromisoformat(start_date) if start_date else datetime.utcnow() - timedelta(days=30)
            end_dt = datetime.fromisoformat(end_date) if end_date else datetime.utcnow()

            # 查询活跃度数据
            activity_data = await self._calculate_user_activity(tenant_id, user_id, start_dt, end_dt)

            return {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "period": {
                    "start_date": start_dt.isoformat(),
                    "end_date": end_dt.isoformat()
                },
                "statistics": activity_data,
                "generated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"获取活跃度统计失败: {str(e)}")

    async def get_user_login_statistics(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        period: str = "month"
    ) -> Dict[str, Any]:
        """
        用户登录统计

        统计用户的登录数据
        """
        try:
            # 计算时间范围
            if period == "day":
                start_dt = datetime.utcnow() - timedelta(days=1)
            elif period == "week":
                start_dt = datetime.utcnow() - timedelta(weeks=1)
            elif period == "month":
                start_dt = datetime.utcnow() - timedelta(days=30)
            else:
                start_dt = datetime.utcnow() - timedelta(days=30)

            # 查询登录统计数据
            login_data = await self._calculate_login_statistics(tenant_id, user_id, start_dt)

            return {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "period": period,
                "statistics": login_data,
                "generated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"获取登录统计失败: {str(e)}")

    async def export_user_data(
        self,
        tenant_id: str,
        export_type: str = "basic",
        user_ids: Optional[List[str]] = None,
        format: str = "json"
    ) -> Dict[str, Any]:
        """
        导出用户数据

        支持多种格式的用户数据导出
        """
        try:
            # 查询用户数据
            if export_type == "basic":
                data = await self._export_basic_user_data(tenant_id, user_ids)
            elif export_type == "detailed":
                data = await self._export_detailed_user_data(tenant_id, user_ids)
            elif export_type == "audit_logs":
                data = await self._export_user_audit_logs(tenant_id, user_ids)
            else:
                raise ValidationError(f"不支持的导出类型: {export_type}")

            # 生成导出文件
            export_id = f"export_{uuid.uuid4()}"
            file_info = await self._generate_export_file(export_id, data, format)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="EXPORT_USER_DATA",
                resource_type="USER",
                resource_id="batch",
                details={
                    "export_type": export_type,
                    "format": format,
                    "user_count": len(user_ids) if user_ids else "all",
                    "export_id": export_id
                }
            )

            return {
                "export_id": export_id,
                "export_type": export_type,
                "format": format,
                "file_info": file_info,
                "record_count": len(data),
                "exported_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"导出用户数据失败: {str(e)}")

    # ===== 辅助方法实现 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_user_uniqueness(self, tenant_id: str, username: str, email: str, phone: Optional[str] = None):
        """验证用户唯一性"""
        # 检查用户名唯一性
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.username == username,
                self.user_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("用户名已存在")

        # 检查邮箱唯一性
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.email == email,
                self.user_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("邮箱已存在")

        # 检查手机号唯一性（如果提供）
        if phone:
            stmt = select(self.user_model).where(
                and_(
                    self.user_model.tenant_id == tenant_id,
                    self.user_model.phone == phone,
                    self.user_model.status != "deleted"
                )
            )
            result = await self.session.execute(stmt)
            if result.scalar_one_or_none():
                raise DuplicateResourceError("手机号已存在")

    async def _validate_password_policy(self, tenant_id: str, password: str):
        """验证密码策略"""
        # 基本密码策略验证
        if len(password) < 8:
            raise ValidationError("密码长度不能少于8位")

        if not any(c.isupper() for c in password):
            raise ValidationError("密码必须包含大写字母")

        if not any(c.islower() for c in password):
            raise ValidationError("密码必须包含小写字母")

        if not any(c.isdigit() for c in password):
            raise ValidationError("密码必须包含数字")

        # TODO: 从租户配置中获取更详细的密码策略

    async def _assign_default_roles(self, user_id: str, tenant_id: str):
        """分配默认角色"""
        # TODO: 查询租户的默认角色配置
        # 暂时跳过角色分配，等角色模块实现后再完善
        pass

    async def _generate_activation_token(self, user_id: str) -> str:
        """生成激活令牌"""
        activation_token = f"activate_{uuid.uuid4()}"

        # 存储激活令牌到Redis，有效期24小时
        await self.redis_repo.set(
            f"activation_token:{activation_token}",
            {"user_id": user_id, "created_at": datetime.utcnow().isoformat()},
            ttl=86400
        )

        return activation_token

    async def _send_welcome_email(self, user_id: str, email: str, username: str, activation_token: Optional[str] = None):
        """发送欢迎邮件"""
        # TODO: 集成邮件服务
        # 暂时只记录日志
        print(f"发送欢迎邮件到 {email}，用户名: {username}，激活令牌: {activation_token}")

    async def _create_audit_log(self, tenant_id: str, user_id: Optional[str], action: str, resource_type: str, resource_id: str, details: Dict[str, Any]):
        """创建审计日志"""
        # TODO: 实现审计日志记录
        # 暂时只记录到控制台
        print(f"审计日志: {action} - {resource_type}:{resource_id} by {user_id} in {tenant_id}")

    async def _cache_user_info(self, user_id: str, user_info: Dict[str, Any]):
        """缓存用户信息"""
        await self.redis_repo.set(
            f"user_info:{user_id}",
            user_info,
            ttl=3600  # 1小时过期
        )

    async def _get_user_by_id(self, user_id: str, tenant_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(
            and_(
                self.user_model.user_id == user_id,
                self.user_model.tenant_id == tenant_id,
                self.user_model.status != "deleted"
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_roles_exist(self, tenant_id: str, role_ids: List[str]) -> List[str]:
        """验证角色存在性和有效性"""
        stmt = select(self.role_model).where(
            and_(
                self.role_model.role_id.in_(role_ids),
                self.role_model.tenant_id == tenant_id,
                self.role_model.status == CommonStatus.ACTIVE
            )
        )
        result = await self.session.execute(stmt)
        valid_roles = result.scalars().all()

        valid_role_ids = [role.role_id for role in valid_roles]
        invalid_roles = [role_id for role_id in role_ids if role_id not in valid_role_ids]

        if invalid_roles:
            raise NotFoundError(f"以下角色不存在或未激活: {invalid_roles}")

        return valid_role_ids

    async def _get_user_existing_roles(self, user_id: str, role_ids: List[str]) -> List[str]:
        """获取用户已拥有的角色"""
        stmt = select(self.user_role_model.role_id).where(
            and_(
                self.user_role_model.user_id == user_id,
                self.user_role_model.role_id.in_(role_ids),
                or_(
                    self.user_role_model.expires_at.is_(None),
                    self.user_role_model.expires_at > datetime.utcnow()
                )
            )
        )
        result = await self.session.execute(stmt)
        return [row[0] for row in result.fetchall()]

    async def _get_user_all_roles(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户所有有效角色"""
        stmt = select(
            self.user_role_model.role_id,
            self.role_model.name,
            self.role_model.code,
            self.user_role_model.assigned_at,
            self.user_role_model.assignment_type,
            self.user_role_model.expires_at
        ).join(
            self.role_model,
            self.user_role_model.role_id == self.role_model.role_id
        ).where(
            and_(
                self.user_role_model.user_id == user_id,
                or_(
                    self.user_role_model.expires_at.is_(None),
                    self.user_role_model.expires_at > datetime.utcnow()
                )
            )
        )
        result = await self.session.execute(stmt)

        roles = []
        for row in result.fetchall():
            roles.append({
                "role_id": row[0],
                "role_name": row[1],
                "role_code": row[2],
                "assigned_at": row[3].isoformat() if row[3] else None,
                "assignment_type": row[4],
                "expires_at": row[5].isoformat() if row[5] else None
            })

        return roles

    async def _get_role_info(self, role_id: str) -> Dict[str, Any]:
        """获取角色信息"""
        stmt = select(self.role_model).where(self.role_model.role_id == role_id)
        result = await self.session.execute(stmt)
        role = result.scalar_one_or_none()

        if not role:
            return {"name": "", "code": ""}

        return {
            "name": role.name,
            "code": role.code,
            "description": getattr(role, 'description', '')
        }

    async def _check_role_dependencies(self, user_id: str, role_ids: List[str]) -> Dict[str, Any]:
        """检查角色依赖关系"""
        # TODO: 实现角色依赖检查逻辑
        # 这里应该检查是否有其他业务依赖这些角色
        # 例如：用户创建的资源、分配的权限等

        return {
            "has_dependencies": False,
            "dependencies": []
        }

    async def _clear_user_permissions_cache(self, user_id: str):
        """清除用户权限缓存"""
        cache_keys = [
            f"user_permissions:{user_id}",
            f"user_roles:{user_id}",
            f"user_info:{user_id}"
        ]

        for key in cache_keys:
            await self.redis_repo.delete(key)

    async def _get_user_effective_permissions(
        self,
        user_id: str,
        tenant_id: str,
        resource_type: Optional[str] = None,
        include_inherited: bool = True
    ) -> Dict[str, Any]:
        """获取用户有效权限"""
        # 查询用户角色
        user_roles = await self._get_user_all_roles(user_id)
        role_ids = [role["role_id"] for role in user_roles]

        if not role_ids:
            return {
                "user_id": user_id,
                "permissions": [],
                "total_count": 0,
                "resource_types": []
            }

        # 查询角色权限
        stmt = select(
            self.permission_model.permission_id,
            self.permission_model.code,
            self.permission_model.name,
            self.permission_model.resource_type,
            self.permission_model.action,
            self.role_permission_model.role_id
        ).join(
            self.role_permission_model,
            self.permission_model.permission_id == self.role_permission_model.permission_id
        ).where(
            and_(
                self.role_permission_model.role_id.in_(role_ids),
                self.permission_model.status == CommonStatus.ACTIVE
            )
        )

        if resource_type:
            stmt = stmt.where(self.permission_model.resource_type == resource_type)

        result = await self.session.execute(stmt)

        permissions = []
        resource_types = set()

        for row in result.fetchall():
            permission_data = {
                "permission_id": row[0],
                "code": row[1],
                "name": row[2],
                "resource_type": row[3],
                "action": row[4],
                "source_role_id": row[5]
            }
            permissions.append(permission_data)
            resource_types.add(row[3])

        return {
            "user_id": user_id,
            "permissions": permissions,
            "total_count": len(permissions),
            "resource_types": list(resource_types),
            "roles": user_roles
        }

    async def _analyze_permission_sources(
        self,
        user_id: str,
        tenant_id: str,
        permission_code: str
    ) -> List[Dict[str, Any]]:
        """分析权限来源"""
        sources = []

        # 查询通过角色获得的权限
        stmt = select(
            self.role_model.role_id,
            self.role_model.name,
            self.role_model.code,
            self.user_role_model.assigned_at,
            self.user_role_model.assignment_type
        ).join(
            self.user_role_model,
            self.role_model.role_id == self.user_role_model.role_id
        ).join(
            self.role_permission_model,
            self.role_model.role_id == self.role_permission_model.role_id
        ).join(
            self.permission_model,
            self.role_permission_model.permission_id == self.permission_model.permission_id
        ).where(
            and_(
                self.user_role_model.user_id == user_id,
                self.permission_model.code == permission_code,
                self.permission_model.status == CommonStatus.ACTIVE,
                or_(
                    self.user_role_model.expires_at.is_(None),
                    self.user_role_model.expires_at > datetime.utcnow()
                )
            )
        )

        result = await self.session.execute(stmt)

        for row in result.fetchall():
            sources.append({
                "source_type": "role",
                "source_id": row[0],
                "source_name": row[1],
                "source_code": row[2],
                "granted_at": row[3].isoformat() if row[3] else None,
                "assignment_type": row[4]
            })

        return sources

    async def _get_user_role_history(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取用户角色历史"""
        # TODO: 实现角色历史查询
        # 这需要一个专门的角色历史表来记录角色分配和移除的历史
        # 目前返回当前角色信息作为示例

        current_roles = await self._get_user_all_roles(user_id)

        history = []
        for role in current_roles:
            history.append({
                "action": "assign",
                "role_id": role["role_id"],
                "role_name": role["role_name"],
                "role_code": role["role_code"],
                "timestamp": role["assigned_at"],
                "assignment_type": role["assignment_type"],
                "expires_at": role["expires_at"],
                "operator_id": None,  # TODO: 记录操作者
                "reason": None  # TODO: 记录操作原因
            })

        return history[:limit]

    async def _get_user_active_sessions(
        self,
        user_id: str,
        status: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取用户活跃会话"""
        # 从Redis查询会话信息
        pattern = f"session:user:{user_id}:*"
        session_keys = await self.redis_repo.keys(pattern)

        sessions = []
        for key in session_keys[:limit]:
            session_data = await self.redis_repo.get(key)
            if session_data:
                if status is None or session_data.get("status") == status:
                    sessions.append({
                        "session_id": session_data.get("session_id"),
                        "device_info": session_data.get("device_info", {}),
                        "ip_address": session_data.get("ip_address"),
                        "user_agent": session_data.get("user_agent"),
                        "created_at": session_data.get("created_at"),
                        "last_activity": session_data.get("last_activity"),
                        "status": session_data.get("status", "active")
                    })

        return sessions

    async def _get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        session_data = await self.redis_repo.get(f"session:{session_id}")
        return session_data

    async def _terminate_session(self, session_id: str, reason: Optional[str] = None):
        """终止会话"""
        # 删除会话数据
        await self.redis_repo.delete(f"session:{session_id}")

        # 将令牌加入黑名单
        # TODO: 实现令牌黑名单逻辑

        # 记录会话终止信息
        await self.redis_repo.set(
            f"session:terminated:{session_id}",
            {
                "terminated_at": datetime.utcnow().isoformat(),
                "reason": reason
            },
            ttl=86400  # 保留24小时
        )


# ===== TODO: 未完成的功能清单 =====
"""
以下功能尚未实现，需要在后续开发中完成：

1. 数据库操作相关：
   - 用户数据的CRUD操作实现
   - 用户角色关联的管理
   - 用户权限查询和缓存
   - 用户状态变更的数据库操作

2. 安全功能相关：
   - 密码加密和验证
   - 验证码生成和验证
   - 激活令牌管理
   - 会话管理和清理

3. 外部服务集成：
   - 邮件服务集成（发送激活邮件、通知邮件）
   - 短信服务集成（发送验证码）
   - 文件存储服务集成（头像上传）

4. 缓存管理：
   - 用户信息缓存策略
   - 权限信息缓存管理
   - 验证码缓存管理
   - 会话信息缓存

5. 审计日志：
   - 用户操作审计日志记录
   - 敏感操作的详细记录
   - 日志查询和分析功能

6. 业务规则验证：
   - 用户名、邮箱、手机号唯一性验证
   - 密码策略验证
   - 状态转换规则验证
   - 权限验证

7. 批量操作功能：
   - 批量用户导入导出
   - 批量状态更新
   - 批量角色分配

8. 统计分析功能：
   - 用户活跃度统计
   - 登录统计分析
   - 用户行为分析

注意：这些功能需要与其他模块（如认证模块、权限模块、审计模块等）协同开发。
"""
