"""
系统配置服务

提供系统配置的管理功能，支持全局配置和租户级配置
支持配置继承、敏感配置加密、配置分类管理等功能
"""

import uuid
import json
import base64
from datetime import datetime
from typing import Dict, Any, Optional, List, Type, Union
from cryptography.fernet import Fernet

from sqlalchemy import select, func, and_, or_, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.exceptions.exceptions import (
    ValidationError, DuplicateResourceError, NotFoundError,
    DatabaseError, BusinessError
)
from domain_common.models.iam_models import (
    Tenant, SystemConfig, AuditLog
)


class SystemConfigService:
    """系统配置服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        tenant_model: Type[Tenant],
        system_config_model: Type[SystemConfig],
        audit_log_model: Type[AuditLog],
        encryption_key: Optional[str] = None
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 业务模型
        self.tenant_model = tenant_model
        self.system_config_model = system_config_model
        self.audit_log_model = audit_log_model

        # 加密密钥
        if encryption_key:
            self.cipher = Fernet(encryption_key.encode())
        else:
            # 生成默认密钥（生产环境应该使用外部密钥）
            self.cipher = Fernet(Fernet.generate_key())

        # 缓存键前缀
        self.config_cache_prefix = "system_config:"
        self.tenant_config_cache_prefix = "tenant_config:"

        # 配置分类
        self.categories = ["general", "security", "notification", "integration", "feature"]

        # 系统默认配置
        self.default_configs = {
            # 安全配置
            "password_policy.min_length": 8,
            "password_policy.require_uppercase": True,
            "password_policy.require_lowercase": True,
            "password_policy.require_numbers": True,
            "password_policy.require_symbols": False,
            "password_policy.max_age_days": 90,
            "session.timeout_hours": 8,
            "session.max_concurrent": 10,
            "login.max_attempts": 5,
            "login.lockout_minutes": 30,
            "mfa.enabled": False,
            "mfa.required_for_admin": True,
            
            # 通知配置
            "notification.email_enabled": True,
            "notification.sms_enabled": False,
            "notification.login_notification": False,
            "notification.security_alert": True,
            
            # 功能配置
            "feature.user_registration": True,
            "feature.password_reset": True,
            "feature.social_login": False,
            "feature.api_access": True,
            
            # 通用配置
            "general.timezone": "UTC",
            "general.date_format": "YYYY-MM-DD",
            "general.language": "zh-CN",
            "general.page_size": 20
        }

    # ===== 配置管理 =====

    async def set_config(
        self,
        tenant_id: Optional[str],
        config_key: str,
        config_value: Union[str, int, float, bool, Dict[str, Any], List[Any]],
        description: Optional[str] = None,
        is_sensitive: bool = False,
        category: str = "general"
    ) -> Dict[str, Any]:
        """
        设置配置
        
        设置系统或租户级配置项
        """
        try:
            # 验证分类
            if category not in self.categories:
                raise ValidationError(f"无效的配置分类: {category}")

            # 验证租户（如果指定）
            if tenant_id:
                tenant = await self._get_tenant_by_id(tenant_id)
                if not tenant:
                    raise NotFoundError("租户不存在")

            # 查找现有配置
            existing_config = await self._get_config_record(tenant_id, config_key)
            previous_value = None

            if existing_config:
                # 更新现有配置
                if existing_config.is_sensitive:
                    previous_value = "[敏感配置]"
                else:
                    previous_value = existing_config.config_value

                existing_config.config_value = self._encrypt_value(config_value) if is_sensitive else config_value
                existing_config.description = description
                existing_config.is_sensitive = is_sensitive
                existing_config.category = category
                existing_config.updated_at = datetime.utcnow()
                
                config_id = existing_config.config_id
            else:
                # 创建新配置
                config_id = f"config_{uuid.uuid4()}"
                
                config = self.system_config_model(
                    config_id=config_id,
                    tenant_id=tenant_id,
                    config_key=config_key,
                    config_value=self._encrypt_value(config_value) if is_sensitive else config_value,
                    description=description,
                    is_sensitive=is_sensitive,
                    category=category,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.session.add(config)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                action="SET_CONFIG",
                resource_type="SYSTEM_CONFIG",
                resource_id=config_id,
                details={
                    "config_key": config_key,
                    "category": category,
                    "is_sensitive": is_sensitive,
                    "has_previous_value": previous_value is not None
                }
            )

            await self.session.commit()

            # 清除缓存
            await self._clear_config_cache(tenant_id, config_key)

            return {
                "config_id": config_id,
                "config_key": config_key,
                "previous_value": previous_value,
                "new_value": config_value if not is_sensitive else "[敏感配置]",
                "updated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"设置配置失败: {str(e)}")

    async def get_config(
        self,
        tenant_id: Optional[str],
        config_key: str,
        include_inherited: bool = True
    ) -> Dict[str, Any]:
        """
        获取配置
        
        获取指定配置项的值，支持配置继承
        """
        try:
            # 尝试从缓存获取
            cache_key = f"{self.config_cache_prefix}{tenant_id or 'global'}:{config_key}"
            cached_config = await self.redis_repo.get(cache_key)
            if cached_config:
                return cached_config

            config_value = None
            source = "default"
            is_inherited = False

            # 1. 查找租户级配置
            if tenant_id and include_inherited:
                tenant_config = await self._get_config_record(tenant_id, config_key)
                if tenant_config:
                    config_value = self._decrypt_value(tenant_config.config_value, tenant_config.is_sensitive)
                    source = "tenant"

            # 2. 查找全局配置
            if config_value is None and include_inherited:
                global_config = await self._get_config_record(None, config_key)
                if global_config:
                    config_value = self._decrypt_value(global_config.config_value, global_config.is_sensitive)
                    source = "global"
                    is_inherited = tenant_id is not None

            # 3. 使用默认配置
            if config_value is None:
                config_value = self.default_configs.get(config_key)
                if config_value is None:
                    raise NotFoundError(f"配置不存在: {config_key}")
                is_inherited = tenant_id is not None or source != "default"

            result = {
                "config_key": config_key,
                "config_value": config_value,
                "is_inherited": is_inherited,
                "source": source
            }

            # 缓存结果
            await self.redis_repo.set(cache_key, result, ttl=3600)

            return result

        except Exception as e:
            if isinstance(e, NotFoundError):
                raise
            raise BusinessError(f"获取配置失败: {str(e)}")

    async def query_configs(
        self,
        tenant_id: Optional[str] = None,
        category: Optional[str] = None,
        keyword: Optional[str] = None,
        include_inherited: bool = True,
        include_sensitive: bool = False,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """
        查询配置列表
        
        支持多条件查询和分页
        """
        try:
            # 构建查询条件
            conditions = []
            
            if tenant_id:
                conditions.append(self.system_config_model.tenant_id == tenant_id)
            elif not include_inherited:
                conditions.append(self.system_config_model.tenant_id.is_(None))

            if category:
                conditions.append(self.system_config_model.category == category)

            if keyword:
                keyword_condition = or_(
                    self.system_config_model.config_key.ilike(f"%{keyword}%"),
                    self.system_config_model.description.ilike(f"%{keyword}%")
                )
                conditions.append(keyword_condition)

            if not include_sensitive:
                conditions.append(self.system_config_model.is_sensitive == False)

            # 查询总数
            count_stmt = select(func.count(self.system_config_model.config_id))
            if conditions:
                count_stmt = count_stmt.where(and_(*conditions))
            total_result = await self.session.execute(count_stmt)
            total = total_result.scalar()

            # 分页查询
            offset = (page - 1) * page_size
            stmt = select(self.system_config_model)
            if conditions:
                stmt = stmt.where(and_(*conditions))
            stmt = stmt.order_by(self.system_config_model.category, self.system_config_model.config_key)
            stmt = stmt.offset(offset).limit(page_size)
            
            result = await self.session.execute(stmt)
            configs = result.scalars().all()

            # 构建配置列表
            config_list = []
            for config in configs:
                config_value = config.config_value
                if config.is_sensitive:
                    config_value = "[敏感配置]" if not include_sensitive else self._decrypt_value(config_value, True)

                config_info = {
                    "config_id": config.config_id,
                    "tenant_id": config.tenant_id,
                    "config_key": config.config_key,
                    "config_value": config_value,
                    "description": config.description,
                    "is_sensitive": config.is_sensitive,
                    "category": config.category,
                    "is_inherited": False,  # 直接查询的配置不是继承的
                    "created_at": config.created_at.isoformat(),
                    "updated_at": config.updated_at.isoformat() if config.updated_at else None
                }
                config_list.append(config_info)

            # 如果包含继承配置且查询的是租户配置，添加全局和默认配置
            if include_inherited and tenant_id:
                config_list = await self._merge_inherited_configs(config_list, tenant_id, category, keyword)

            return {
                "configs": config_list,
                "total": len(config_list),  # 包含继承配置后的总数
                "page": page,
                "page_size": page_size,
                "has_next": offset + page_size < total
            }

        except Exception as e:
            raise BusinessError(f"查询配置失败: {str(e)}")

    async def delete_config(
        self,
        tenant_id: Optional[str],
        config_key: str
    ) -> Dict[str, Any]:
        """
        删除配置

        删除指定配置项
        """
        try:
            # 查找配置
            config = await self._get_config_record(tenant_id, config_key)
            if not config:
                raise NotFoundError("配置不存在")

            # 检查是否为系统核心配置
            if self._is_system_core_config(config_key):
                raise ValidationError("系统核心配置不可删除")

            # 删除配置
            await self.session.delete(config)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                action="DELETE_CONFIG",
                resource_type="SYSTEM_CONFIG",
                resource_id=config.config_id,
                details={
                    "config_key": config_key,
                    "category": config.category
                }
            )

            await self.session.commit()

            # 清除缓存
            await self._clear_config_cache(tenant_id, config_key)

            return {
                "config_key": config_key,
                "deleted_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (NotFoundError, ValidationError)):
                raise
            raise BusinessError(f"删除配置失败: {str(e)}")

    async def batch_set_configs(
        self,
        tenant_id: Optional[str],
        configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        批量设置配置

        批量设置多个配置项
        """
        try:
            success_count = 0
            failed_count = 0
            failed_configs = []

            for config_data in configs:
                try:
                    await self.set_config(
                        tenant_id=tenant_id,
                        config_key=config_data["config_key"],
                        config_value=config_data["config_value"],
                        description=config_data.get("description"),
                        is_sensitive=config_data.get("is_sensitive", False),
                        category=config_data.get("category", "general")
                    )
                    success_count += 1
                except Exception as e:
                    failed_count += 1
                    failed_configs.append({
                        "config_key": config_data["config_key"],
                        "error": str(e)
                    })

            return {
                "success_count": success_count,
                "failed_count": failed_count,
                "failed_configs": failed_configs,
                "updated_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            raise BusinessError(f"批量设置配置失败: {str(e)}")

    async def reset_configs(
        self,
        tenant_id: Optional[str],
        category: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        重置配置

        重置配置到默认值
        """
        try:
            # 构建删除条件
            conditions = []

            if tenant_id:
                conditions.append(self.system_config_model.tenant_id == tenant_id)
            else:
                conditions.append(self.system_config_model.tenant_id.is_(None))

            if category:
                conditions.append(self.system_config_model.category == category)

            # 查询要删除的配置
            stmt = select(self.system_config_model).where(and_(*conditions))
            result = await self.session.execute(stmt)
            configs_to_delete = result.scalars().all()

            # 过滤掉系统核心配置
            deletable_configs = [
                config for config in configs_to_delete
                if not self._is_system_core_config(config.config_key)
            ]

            # 删除配置
            for config in deletable_configs:
                await self.session.delete(config)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                action="RESET_CONFIGS",
                resource_type="SYSTEM_CONFIG",
                resource_id=None,
                details={
                    "category": category,
                    "reset_count": len(deletable_configs)
                }
            )

            await self.session.commit()

            # 清除相关缓存
            await self._clear_all_config_cache(tenant_id)

            return {
                "reset_count": len(deletable_configs),
                "category": category,
                "reset_at": datetime.utcnow().isoformat()
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"重置配置失败: {str(e)}")

    # ===== 辅助方法 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_config_record(self, tenant_id: Optional[str], config_key: str):
        """获取配置记录"""
        stmt = select(self.system_config_model).where(
            and_(
                self.system_config_model.tenant_id == tenant_id,
                self.system_config_model.config_key == config_key
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    def _encrypt_value(self, value: Any) -> str:
        """加密配置值"""
        if isinstance(value, (dict, list)):
            value_str = json.dumps(value, ensure_ascii=False)
        else:
            value_str = str(value)

        encrypted_bytes = self.cipher.encrypt(value_str.encode())
        return base64.b64encode(encrypted_bytes).decode()

    def _decrypt_value(self, encrypted_value: Any, is_sensitive: bool) -> Any:
        """解密配置值"""
        if not is_sensitive:
            return encrypted_value

        try:
            encrypted_bytes = base64.b64decode(encrypted_value.encode())
            decrypted_str = self.cipher.decrypt(encrypted_bytes).decode()

            # 尝试解析为JSON
            try:
                return json.loads(decrypted_str)
            except json.JSONDecodeError:
                return decrypted_str
        except Exception:
            return "[解密失败]"

    def _is_system_core_config(self, config_key: str) -> bool:
        """检查是否为系统核心配置"""
        core_configs = [
            "password_policy.min_length",
            "session.timeout_hours",
            "login.max_attempts"
        ]
        return config_key in core_configs

    async def _merge_inherited_configs(
        self,
        tenant_configs: List[Dict[str, Any]],
        tenant_id: str,
        category: Optional[str],
        keyword: Optional[str]
    ) -> List[Dict[str, Any]]:
        """合并继承的配置"""
        # 获取租户配置的键
        tenant_keys = {config["config_key"] for config in tenant_configs}

        # 查询全局配置
        global_conditions = [self.system_config_model.tenant_id.is_(None)]
        if category:
            global_conditions.append(self.system_config_model.category == category)
        if keyword:
            keyword_condition = or_(
                self.system_config_model.config_key.ilike(f"%{keyword}%"),
                self.system_config_model.description.ilike(f"%{keyword}%")
            )
            global_conditions.append(keyword_condition)

        stmt = select(self.system_config_model).where(and_(*global_conditions))
        result = await self.session.execute(stmt)
        global_configs = result.scalars().all()

        # 添加不在租户配置中的全局配置
        for global_config in global_configs:
            if global_config.config_key not in tenant_keys:
                config_value = global_config.config_value
                if global_config.is_sensitive:
                    config_value = "[敏感配置]"

                config_info = {
                    "config_id": global_config.config_id,
                    "tenant_id": None,
                    "config_key": global_config.config_key,
                    "config_value": config_value,
                    "description": global_config.description,
                    "is_sensitive": global_config.is_sensitive,
                    "category": global_config.category,
                    "is_inherited": True,
                    "created_at": global_config.created_at.isoformat(),
                    "updated_at": global_config.updated_at.isoformat() if global_config.updated_at else None
                }
                tenant_configs.append(config_info)

        # 添加默认配置（如果不在租户和全局配置中）
        all_keys = {config["config_key"] for config in tenant_configs}
        for default_key, default_value in self.default_configs.items():
            if default_key not in all_keys:
                # 应用过滤条件
                if category and not default_key.startswith(f"{category}."):
                    continue
                if keyword and keyword.lower() not in default_key.lower():
                    continue

                config_info = {
                    "config_id": f"default_{default_key}",
                    "tenant_id": None,
                    "config_key": default_key,
                    "config_value": default_value,
                    "description": f"系统默认配置: {default_key}",
                    "is_sensitive": False,
                    "category": default_key.split(".")[0] if "." in default_key else "general",
                    "is_inherited": True,
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": None
                }
                tenant_configs.append(config_info)

        return tenant_configs

    async def _clear_config_cache(self, tenant_id: Optional[str], config_key: str):
        """清除配置缓存"""
        cache_key = f"{self.config_cache_prefix}{tenant_id or 'global'}:{config_key}"
        await self.redis_repo.delete(cache_key)

    async def _clear_all_config_cache(self, tenant_id: Optional[str]):
        """清除所有配置缓存"""
        pattern = f"{self.config_cache_prefix}{tenant_id or 'global'}:*"
        keys = await self.redis_repo.scan_keys(pattern)
        if keys:
            await self.redis_repo.delete(*keys)

    async def _create_audit_log(self, tenant_id: Optional[str], action: str, resource_type: str, resource_id: Optional[str], details: Dict[str, Any]):
        """创建审计日志"""
        # TODO: 实现审计日志记录
        print(f"审计日志: {action} - {resource_type}:{resource_id} in {tenant_id} - {details}")
