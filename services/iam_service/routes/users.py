"""
用户管理路由

提供用户的创建、查询、更新、删除、注册、激活等接口
支持用户生命周期管理、信息维护、状态管理和安全功能
"""

from fastapi import APIRouter, Depends
from dependency_injector.wiring import inject, Provide
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.user_service import UserService

router = APIRouter()


# ===== 请求模型 =====

class CreateUserRequest(BaseModel):
    """
    创建用户请求模型

    管理员创建用户账户，包含用户基本信息和配置参数
    """
    tenant_id: str = Field(
        ...,
        description="租户ID，用户所属的租户标识符",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    username: str = Field(
        ...,
        description="用户名，租户内唯一，只能包含字母、数字、下划线和连字符",
        min_length=3,
        max_length=50,
        examples=["john_doe"]
    )
    email: str = Field(
        ...,
        description="邮箱地址，租户内唯一，用于登录和通知",
        examples=["<EMAIL>"]
    )
    phone: Optional[str] = Field(
        None,
        description="手机号码，租户内唯一，用于登录和验证",
        examples=["13800138001"]
    )
    nickname: Optional[str] = Field(
        None,
        description="用户昵称，用于显示的友好名称",
        max_length=100,
        examples=["约翰"]
    )
    password: str = Field(
        ...,
        description="用户密码，需要符合租户密码策略",
        min_length=6,
        examples=["SecurePass123!"]
    )
    status: Optional[str] = Field(
        "pending",
        description="用户初始状态：pending(待激活)、active(活跃)、inactive(非活跃)",
        examples=["pending"]
    )
    profile: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="用户资料信息，包含部门、职位、员工ID等扩展信息",
        examples=[{
            "department": "技术部",
            "position": "工程师",
            "employee_id": "EMP001",
            "avatar": "https://domain.com/avatars/default.jpg"
        }]
    )
    send_welcome_email: bool = Field(
        True,
        description="是否发送欢迎邮件，包含账户激活链接",
        examples=[True]
    )


class UserRegisterRequest(BaseModel):
    """
    用户注册请求模型

    用户自主注册功能，需要验证码验证
    """
    tenant_code: str = Field(
        ...,
        description="租户编码，用于确定用户注册到哪个租户",
        examples=["DEMO_CORP"]
    )
    username: str = Field(
        ...,
        description="用户名，租户内唯一",
        min_length=3,
        max_length=50,
        examples=["jane_doe"]
    )
    email: str = Field(
        ...,
        description="邮箱地址，租户内唯一",
        examples=["<EMAIL>"]
    )
    phone: Optional[str] = Field(
        None,
        description="手机号码，租户内唯一",
        examples=["13800138002"]
    )
    nickname: Optional[str] = Field(
        None,
        description="用户昵称",
        max_length=100,
        examples=["简"]
    )
    password: str = Field(
        ...,
        description="用户密码",
        min_length=6,
        examples=["UserPass123!"]
    )
    verification_code: str = Field(
        ...,
        description="验证码，用于验证邮箱或手机号",
        examples=["123456"]
    )
    code_id: str = Field(
        ...,
        description="验证码ID，与验证码配对使用",
        examples=["code_550e8400-e29b-41d4-a716-************"]
    )
    agree_terms: bool = Field(
        ...,
        description="是否同意服务条款，必须为true",
        examples=[True]
    )


class ActivateUserRequest(BaseModel):
    """
    激活用户账户请求模型

    通过激活令牌激活用户账户
    """
    activation_token: str = Field(
        ...,
        description="激活令牌，通过邮件发送给用户",
        examples=["activate_550e8400-e29b-41d4-a716-************"]
    )


class ListUsersRequest(BaseModel):
    """
    查询用户列表请求模型

    支持分页查询、关键词搜索和条件筛选
    """
    tenant_id: str = Field(
        ...,
        description="租户ID，查询指定租户的用户",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    cursor: Optional[str] = Field(
        None,
        description="分页游标，用于游标分页",
        examples=["2025-01-23T10:30:45.123456"]
    )
    limit: int = Field(
        20,
        description="每页返回的记录数量，范围1-100",
        ge=1,
        le=100,
        examples=[20]
    )
    keyword: Optional[str] = Field(
        None,
        description="搜索关键词，支持用户名、邮箱、昵称模糊搜索",
        max_length=100,
        examples=["john"]
    )
    status: Optional[str] = Field(
        None,
        description="用户状态筛选：active、inactive、pending、locked、deleted",
        examples=["active"]
    )
    role_id: Optional[str] = Field(
        None,
        description="角色ID筛选，查询拥有指定角色的用户",
        examples=["role_550e8400-e29b-41d4-a716-************"]
    )


class GetUserRequest(BaseModel):
    """
    获取用户详情请求模型

    通过用户ID获取指定用户的详细信息
    """
    tenant_id: str = Field(
        ...,
        description="租户ID，确保租户隔离",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户唯一标识符，UUID格式",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    include_permissions: bool = Field(
        False,
        description="是否包含用户权限信息",
        examples=[True]
    )
    include_sessions: bool = Field(
        False,
        description="是否包含用户会话信息",
        examples=[False]
    )


class GetUserProfileRequest(BaseModel):
    """
    获取用户个人资料请求模型

    获取用户的个人资料和偏好设置
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )


class UpdateUserRequest(BaseModel):
    """
    更新用户信息请求模型

    支持部分字段更新，只传入需要修改的字段即可
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    nickname: Optional[str] = Field(
        None,
        description="用户昵称，如需修改则传入新的昵称",
        max_length=100,
        examples=["新昵称"]
    )
    email: Optional[str] = Field(
        None,
        description="邮箱地址，如需修改则传入新的邮箱",
        examples=["<EMAIL>"]
    )
    phone: Optional[str] = Field(
        None,
        description="手机号码，如需修改则传入新的手机号",
        examples=["13800138002"]
    )
    status: Optional[str] = Field(
        None,
        description="用户状态，如需修改则传入新的状态",
        examples=["active"]
    )
    profile: Optional[Dict[str, Any]] = Field(
        None,
        description="用户资料信息，如需修改则传入新的资料",
        examples=[{
            "avatar": "https://domain.com/avatars/new_avatar.jpg",
            "department": "产品部",
            "position": "产品经理",
            "employee_id": "EMP002"
        }]
    )


class UpdateUserPreferencesRequest(BaseModel):
    """
    更新用户偏好设置请求模型

    更新用户的个人偏好和界面设置
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    preferences: Dict[str, Any] = Field(
        ...,
        description="用户偏好设置，包含语言、时区、主题、通知等配置",
        examples=[{
            "language": "en-US",
            "timezone": "America/New_York",
            "theme": "dark",
            "notifications": {
                "email": False,
                "sms": True,
                "push": True
            },
            "dashboard_layout": "compact"
        }]
    )


class UpdateUserStatusRequest(BaseModel):
    """
    用户状态管理请求模型

    更新用户的状态，支持锁定、解锁、暂停等操作
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    status: str = Field(
        ...,
        description="新状态：active、inactive、locked、deleted",
        examples=["locked"]
    )
    reason: Optional[str] = Field(
        None,
        description="状态变更原因",
        max_length=200,
        examples=["多次登录失败"]
    )
    locked_until: Optional[str] = Field(
        None,
        description="锁定截止时间，仅在状态为locked时有效",
        examples=["2025-01-15T12:30:45.123456"]
    )
    send_notification: bool = Field(
        True,
        description="是否发送状态变更通知",
        examples=[True]
    )


class DeleteUserRequest(BaseModel):
    """
    删除用户请求模型

    支持软删除和硬删除两种模式
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="要删除的用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    delete_type: Optional[str] = Field(
        "soft",
        description="删除类型：soft(软删除) 或 hard(硬删除)",
        examples=["soft"]
    )
    reason: Optional[str] = Field(
        None,
        description="删除原因",
        max_length=200,
        examples=["用户申请注销"]
    )
    transfer_data_to: Optional[str] = Field(
        None,
        description="数据转移目标用户ID，将被删除用户的数据转移给指定用户",
        examples=["user_550e8400-e29b-41d4-a716-446655440001"]
    )
    confirmation_code: str = Field(
        ...,
        description="确认码，防止误删除操作",
        examples=["DELETE_USER_123456"]
    )


class ChangePhoneRequest(BaseModel):
    """
    更换手机号请求模型

    需要短信验证码验证的手机号更换
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    old_phone: str = Field(
        ...,
        description="原手机号",
        examples=["13800138001"]
    )
    new_phone: str = Field(
        ...,
        description="新手机号",
        examples=["13800138002"]
    )
    sms_code: str = Field(
        ...,
        description="短信验证码",
        examples=["123456"]
    )
    code_id: str = Field(
        ...,
        description="验证码ID",
        examples=["code_550e8400-e29b-41d4-a716-************"]
    )


class ChangeEmailRequest(BaseModel):
    """
    更换邮箱请求模型

    需要邮箱验证码验证的邮箱更换
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    old_email: str = Field(
        ...,
        description="原邮箱地址",
        examples=["<EMAIL>"]
    )
    new_email: str = Field(
        ...,
        description="新邮箱地址",
        examples=["<EMAIL>"]
    )
    email_code: str = Field(
        ...,
        description="邮箱验证码",
        examples=["123456"]
    )
    code_id: str = Field(
        ...,
        description="验证码ID",
        examples=["code_550e8400-e29b-41d4-a716-************"]
    )


class SendSmsCodeRequest(BaseModel):
    """
    发送短信验证码请求模型

    支持多种场景的短信验证码发送
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    phone: str = Field(
        ...,
        description="手机号码",
        examples=["13800138000"]
    )
    scene: str = Field(
        ...,
        description="使用场景：register、change_phone、reset_password、login",
        examples=["change_phone"]
    )
    user_id: Optional[str] = Field(
        None,
        description="用户ID，某些场景下需要",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )


class SendEmailCodeRequest(BaseModel):
    """
    发送邮箱验证码请求模型

    支持多种场景的邮箱验证码发送
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    email: str = Field(
        ...,
        description="邮箱地址",
        examples=["<EMAIL>"]
    )
    scene: str = Field(
        ...,
        description="使用场景：register、change_email、reset_password、login",
        examples=["change_email"]
    )
    user_id: Optional[str] = Field(
        None,
        description="用户ID，某些场景下需要",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )


class AssignRolesRequest(BaseModel):
    """
    分配角色请求模型

    为用户分配一个或多个角色
    """
    tenant_id: str = Field(
        ...,
        description="租户ID，确保租户隔离",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID，要分配角色的目标用户",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    role_ids: List[str] = Field(
        ...,
        description="角色ID列表，要分配给用户的角色",
        examples=[["role_550e8400-e29b-41d4-a716-446655440001", "role_550e8400-e29b-41d4-a716-446655440002"]]
    )
    assignment_type: Optional[str] = Field(
        "permanent",
        description="分配类型：permanent(永久)、temporary(临时)",
        examples=["permanent"]
    )
    expires_at: Optional[str] = Field(
        None,
        description="过期时间，仅在assignment_type为temporary时有效",
        examples=["2025-12-31T23:59:59.999999"]
    )
    reason: Optional[str] = Field(
        None,
        description="分配原因，用于审计记录",
        max_length=200,
        examples=["用户职位变更，需要管理员权限"]
    )


class RemoveRolesRequest(BaseModel):
    """
    移除角色请求模型

    移除用户的一个或多个角色
    """
    tenant_id: str = Field(
        ...,
        description="租户ID，确保租户隔离",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    user_id: str = Field(
        ...,
        description="用户ID，要移除角色的目标用户",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    role_ids: List[str] = Field(
        ...,
        description="角色ID列表，要从用户移除的角色",
        examples=[["role_550e8400-e29b-41d4-a716-446655440001", "role_550e8400-e29b-41d4-a716-446655440002"]]
    )
    reason: Optional[str] = Field(
        None,
        description="移除原因，用于审计记录",
        max_length=200,
        examples=["用户离职，移除管理员权限"]
    )
    force_remove: bool = Field(
        False,
        description="是否强制移除，忽略依赖检查",
        examples=[False]
    )


# ===== 响应数据模型 =====

class UserProfile(BaseModel):
    """用户资料模型"""
    avatar: Optional[str] = Field(None, description="头像URL", examples=["https://domain.com/avatars/user_xxx.jpg"])
    department: Optional[str] = Field(None, description="部门", examples=["技术部"])
    position: Optional[str] = Field(None, description="职位", examples=["工程师"])
    employee_id: Optional[str] = Field(None, description="员工ID", examples=["EMP001"])


class UserPreferences(BaseModel):
    """用户偏好设置模型"""
    language: str = Field("zh-CN", description="语言设置", examples=["zh-CN"])
    timezone: str = Field("Asia/Shanghai", description="时区设置", examples=["Asia/Shanghai"])
    theme: str = Field("light", description="主题设置", examples=["light"])
    notifications: Dict[str, bool] = Field(
        default_factory=lambda: {"email": True, "sms": False, "push": True},
        description="通知设置",
        examples=[{"email": True, "sms": False, "push": True}]
    )
    dashboard_layout: Optional[str] = Field(None, description="仪表板布局", examples=["compact"])


class UserSecurityInfo(BaseModel):
    """用户安全信息模型"""
    mfa_enabled: bool = Field(False, description="是否启用多因子认证", examples=[False])
    password_expires_at: Optional[str] = Field(None, description="密码过期时间", examples=["2025-04-15T10:00:00.123456"])
    last_password_change: Optional[str] = Field(None, description="最后密码修改时间", examples=["2025-01-10T10:00:00.123456"])
    last_login: Optional[str] = Field(None, description="最后登录时间", examples=["2025-01-15T09:00:00.123456"])
    active_sessions: int = Field(0, description="活跃会话数", examples=[2])


class UserRole(BaseModel):
    """用户角色模型"""
    role_id: str = Field(..., description="角色ID", examples=["role_550e8400-e29b-41d4-a716-************"])
    role_name: str = Field(..., description="角色名称", examples=["普通用户"])
    role_code: str = Field(..., description="角色编码", examples=["USER"])
    assigned_at: str = Field(..., description="分配时间", examples=["2025-01-15T10:30:45.123456"])


class UserResponse(BaseModel):
    """
    用户基本信息响应数据模型

    包含用户的基础信息和当前状态
    """
    user_id: str = Field(
        ...,
        description="用户唯一标识符，UUID格式",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    username: str = Field(
        ...,
        description="用户名",
        examples=["john_doe"]
    )
    email: str = Field(
        ...,
        description="邮箱地址",
        examples=["<EMAIL>"]
    )
    phone: Optional[str] = Field(
        None,
        description="手机号码",
        examples=["13800138001"]
    )
    nickname: Optional[str] = Field(
        None,
        description="用户昵称",
        examples=["约翰"]
    )
    status: str = Field(
        ...,
        description="用户状态：active、inactive、pending、locked、deleted",
        examples=["active"]
    )
    created_at: str = Field(
        ...,
        description="创建时间，ISO 8601格式",
        examples=["2025-01-15T10:30:45.123456"]
    )
    updated_at: Optional[str] = Field(
        None,
        description="最后更新时间，ISO 8601格式",
        examples=["2025-01-15T11:00:00.123456"]
    )
    last_login: Optional[str] = Field(
        None,
        description="最后登录时间",
        examples=["2025-01-15T09:00:00.123456"]
    )
    login_count: int = Field(
        0,
        description="登录次数",
        examples=[25]
    )
    roles: List[UserRole] = Field(
        default_factory=list,
        description="用户角色列表"
    )
    profile: Optional[UserProfile] = Field(
        None,
        description="用户资料信息"
    )


class UserDetailResponse(UserResponse):
    """
    用户详细信息响应数据模型

    继承基本用户信息，并添加详细信息
    """
    failed_login_attempts: int = Field(
        0,
        description="失败登录尝试次数",
        examples=[0]
    )
    locked_until: Optional[str] = Field(
        None,
        description="锁定截止时间",
        examples=[None]
    )
    password_changed_at: Optional[str] = Field(
        None,
        description="密码修改时间",
        examples=["2025-01-10T10:00:00.123456"]
    )
    permissions: List[str] = Field(
        default_factory=list,
        description="用户权限列表",
        examples=[["user:read", "document:read"]]
    )
    security_settings: Optional[UserSecurityInfo] = Field(
        None,
        description="安全设置信息"
    )


class UserProfileResponse(BaseModel):
    """
    用户个人资料响应数据模型

    包含用户资料和偏好设置
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-************"])
    username: str = Field(..., description="用户名", examples=["john_doe"])
    email: str = Field(..., description="邮箱地址", examples=["<EMAIL>"])
    phone: Optional[str] = Field(None, description="手机号码", examples=["13800138001"])
    nickname: Optional[str] = Field(None, description="用户昵称", examples=["约翰"])
    profile: Optional[UserProfile] = Field(None, description="用户资料信息")
    preferences: UserPreferences = Field(..., description="用户偏好设置")
    security_info: UserSecurityInfo = Field(..., description="安全信息摘要")


class CreateUserResponse(UserResponse):
    """
    创建用户响应数据模型

    包含新创建用户的完整信息
    """
    activation_required: bool = Field(
        ...,
        description="是否需要激活",
        examples=[True]
    )


class UserRegisterResponse(BaseModel):
    """
    用户注册响应数据模型

    包含注册结果和激活信息
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-************"])
    username: str = Field(..., description="用户名", examples=["jane_doe"])
    status: str = Field(..., description="用户状态", examples=["pending"])
    activation_token: str = Field(..., description="激活令牌", examples=["activate_550e8400-e29b-41d4-a716-************"])


class ActivateUserResponse(BaseModel):
    """
    激活用户响应数据模型

    包含激活结果
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-************"])
    status: str = Field(..., description="激活后状态", examples=["active"])
    activated_at: str = Field(..., description="激活时间", examples=["2025-01-15T10:30:45.123456"])


class UpdateUserResponse(UserResponse):
    """
    更新用户响应数据模型

    包含更新后的用户信息
    """
    pass


class UpdateUserStatusResponse(BaseModel):
    """
    用户状态更新响应数据模型

    包含状态变更结果
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-************"])
    old_status: str = Field(..., description="原状态", examples=["active"])
    new_status: str = Field(..., description="新状态", examples=["locked"])
    locked_until: Optional[str] = Field(None, description="锁定截止时间", examples=["2025-01-15T12:30:45.123456"])
    updated_at: str = Field(..., description="更新时间", examples=["2025-01-15T10:30:45.123456"])


class DeleteUserResponse(BaseModel):
    """
    删除用户响应数据模型

    包含删除操作的结果
    """
    user_id: str = Field(..., description="被删除的用户ID", examples=["user_550e8400-e29b-41d4-a716-************"])
    status: str = Field(..., description="删除后状态", examples=["deleted"])
    deleted_at: str = Field(..., description="删除时间", examples=["2025-01-15T10:30:45.123456"])
    data_transferred: bool = Field(..., description="是否已转移数据", examples=[True])
    sessions_terminated: int = Field(..., description="终止的会话数", examples=[3])


class ChangeContactResponse(BaseModel):
    """
    更换联系方式响应数据模型

    用于手机号和邮箱更换的响应
    """
    user_id: str = Field(..., description="用户ID", examples=["user_550e8400-e29b-41d4-a716-************"])
    old_value: str = Field(..., description="原值（脱敏）", examples=["138****8001"])
    new_value: str = Field(..., description="新值（脱敏）", examples=["138****8002"])
    changed_at: str = Field(..., description="更换时间", examples=["2025-01-15T10:30:45.123456"])


class SendCodeResponse(BaseModel):
    """
    发送验证码响应数据模型

    包含验证码发送结果
    """
    target: str = Field(..., description="发送目标（脱敏）", examples=["138****8000"])
    code_id: str = Field(..., description="验证码ID", examples=["code_550e8400-e29b-41d4-a716-************"])
    expire_seconds: int = Field(..., description="过期时间（秒）", examples=[300])
    sent_at: str = Field(..., description="发送时间", examples=["2025-01-15T10:30:45.123456"])


class UserListResponse(BaseModel):
    """
    用户列表响应数据模型

    支持分页查询的用户列表数据
    """
    items: List[UserResponse] = Field(
        ...,
        description="用户信息列表，包含当前页的所有用户基本信息"
    )
    total: int = Field(
        ...,
        description="符合查询条件的用户总数量",
        examples=[100]
    )
    next_cursor: Optional[str] = Field(
        None,
        description="下一页游标，用于获取下一页数据，如果为null表示没有更多数据",
        examples=["2025-01-23T10:30:45.123456"]
    )
    has_more: bool = Field(
        ...,
        description="是否还有更多数据可以查询",
        examples=[True]
    )


class AssignRolesResponse(BaseModel):
    """
    分配角色响应数据模型

    包含角色分配操作的结果
    """
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    assigned_roles: List[UserRole] = Field(
        ...,
        description="成功分配的角色列表"
    )
    failed_roles: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="分配失败的角色列表，包含失败原因",
        examples=[[{
            "role_id": "role_550e8400-e29b-41d4-a716-446655440003",
            "error": "角色不存在"
        }]]
    )
    assigned_at: str = Field(
        ...,
        description="分配时间",
        examples=["2025-01-15T10:30:45.123456"]
    )


class RemoveRolesResponse(BaseModel):
    """
    移除角色响应数据模型

    包含角色移除操作的结果
    """
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-************"]
    )
    removed_roles: List[str] = Field(
        ...,
        description="成功移除的角色ID列表",
        examples=[["role_550e8400-e29b-41d4-a716-446655440001"]]
    )
    failed_roles: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="移除失败的角色列表，包含失败原因",
        examples=[[{
            "role_id": "role_550e8400-e29b-41d4-a716-446655440003",
            "error": "用户未拥有此角色"
        }]]
    )
    remaining_roles: List[UserRole] = Field(
        ...,
        description="移除后剩余的角色列表"
    )
    removed_at: str = Field(
        ...,
        description="移除时间",
        examples=["2025-01-15T10:30:45.123456"]
    )


# ===== 响应模型包装类 =====

class CreateUserResponseModel(SuccessResponse[CreateUserResponse]):
    """创建用户响应模型"""
    data: CreateUserResponse = Field(..., description="创建用户结果数据")


class UserRegisterResponseModel(SuccessResponse[UserRegisterResponse]):
    """用户注册响应模型"""
    data: UserRegisterResponse = Field(..., description="用户注册结果数据")


class ActivateUserResponseModel(SuccessResponse[ActivateUserResponse]):
    """激活用户响应模型"""
    data: ActivateUserResponse = Field(..., description="激活用户结果数据")


class UserListResponseModel(SuccessResponse[UserListResponse]):
    """用户列表响应模型"""
    data: UserListResponse = Field(..., description="用户列表数据")


class UserDetailResponseModel(SuccessResponse[UserDetailResponse]):
    """用户详情响应模型"""
    data: UserDetailResponse = Field(..., description="用户详细信息数据")


class UserProfileResponseModel(SuccessResponse[UserProfileResponse]):
    """用户个人资料响应模型"""
    data: UserProfileResponse = Field(..., description="用户个人资料数据")


class UpdateUserResponseModel(SuccessResponse[UpdateUserResponse]):
    """更新用户响应模型"""
    data: UpdateUserResponse = Field(..., description="更新用户结果数据")


class UpdateUserStatusResponseModel(SuccessResponse[UpdateUserStatusResponse]):
    """用户状态更新响应模型"""
    data: UpdateUserStatusResponse = Field(..., description="用户状态更新结果数据")


class DeleteUserResponseModel(SuccessResponse[DeleteUserResponse]):
    """删除用户响应模型"""
    data: DeleteUserResponse = Field(..., description="删除用户结果数据")


class ChangeContactResponseModel(SuccessResponse[ChangeContactResponse]):
    """更换联系方式响应模型"""
    data: ChangeContactResponse = Field(..., description="更换联系方式结果数据")


class SendCodeResponseModel(SuccessResponse[SendCodeResponse]):
    """发送验证码响应模型"""
    data: SendCodeResponse = Field(..., description="发送验证码结果数据")


class AssignRolesResponseModel(SuccessResponse[AssignRolesResponse]):
    """分配角色响应模型"""
    data: AssignRolesResponse = Field(..., description="分配角色结果数据")


class RemoveRolesResponseModel(SuccessResponse[RemoveRolesResponse]):
    """移除角色响应模型"""
    data: RemoveRolesResponse = Field(..., description="移除角色结果数据")


# ===== 路由端点 =====

@router.post(
    "/create",
    summary="创建用户",
    description="""
    管理员创建用户账户

    **功能说明：**
    - 管理员在指定租户下创建新用户
    - 支持设置用户基本信息和资料
    - 可选择发送欢迎邮件和激活链接
    - 自动生成用户ID和初始状态

    **业务规则：**
    - 用户名在租户内唯一
    - 邮箱在租户内唯一
    - 手机号在租户内唯一（如果提供）
    - 密码需符合租户密码策略

    **返回数据：**
    - 新创建的用户完整信息
    - 是否需要激活的标识
    """,
    response_model=CreateUserResponseModel,
    responses={
        200: {"description": "用户创建成功"},
        400: {"description": "请求参数错误"},
        409: {"description": "用户名或邮箱已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def create_user(
    request: BaseRequest[CreateUserRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    创建用户

    管理员创建用户账户，包含完整的初始化流程
    """
    create_user_params = request.data
    result = await user_service.create_user(
        tenant_id=create_user_params.tenant_id,
        username=create_user_params.username,
        email=create_user_params.email,
        phone=create_user_params.phone,
        nickname=create_user_params.nickname,
        password=create_user_params.password,
        status=create_user_params.status,
        profile=create_user_params.profile,
        send_welcome_email=create_user_params.send_welcome_email
    )
    return success_response(result, message="用户创建成功，激活邮件已发送")


@router.post(
    "/register",
    summary="用户注册",
    description="""
    用户自主注册功能

    **功能说明：**
    - 用户通过租户编码自主注册
    - 需要验证码验证邮箱或手机号
    - 必须同意服务条款
    - 注册后需要激活才能使用

    **业务规则：**
    - 租户编码必须存在且处于活跃状态
    - 验证码必须有效且未过期
    - 用户名、邮箱在租户内唯一
    - 必须同意服务条款

    **返回数据：**
    - 注册用户的基本信息
    - 激活令牌（用于邮件激活）
    """,
    response_model=UserRegisterResponseModel,
    responses={
        200: {"description": "注册成功"},
        400: {"description": "请求参数错误或验证码无效"},
        404: {"description": "租户不存在"},
        409: {"description": "用户名或邮箱已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def register_user(
    request: BaseRequest[UserRegisterRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    用户注册

    用户自主注册功能，需要验证码验证
    """
    register_params = request.data
    result = await user_service.register_user(
        tenant_code=register_params.tenant_code,
        username=register_params.username,
        email=register_params.email,
        phone=register_params.phone,
        nickname=register_params.nickname,
        password=register_params.password,
        verification_code=register_params.verification_code,
        code_id=register_params.code_id,
        agree_terms=register_params.agree_terms
    )
    return success_response(result, message="注册成功，请查收激活邮件")


@router.post(
    "/activate",
    summary="激活用户账户",
    description="""
    通过激活令牌激活用户账户

    **功能说明：**
    - 用户通过邮件中的激活链接激活账户
    - 激活后用户状态变为活跃
    - 激活令牌一次性使用，激活后失效

    **业务规则：**
    - 激活令牌必须有效且未过期
    - 用户状态必须为待激活
    - 激活令牌只能使用一次

    **返回数据：**
    - 激活后的用户信息
    - 激活时间
    """,
    response_model=ActivateUserResponseModel,
    responses={
        200: {"description": "账户激活成功"},
        400: {"description": "激活令牌无效或已过期"},
        404: {"description": "用户不存在"},
        409: {"description": "用户已激活"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def activate_user(
    request: BaseRequest[ActivateUserRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    激活用户账户

    通过激活令牌激活用户账户
    """
    activate_params = request.data
    result = await user_service.activate_user(
        activation_token=activate_params.activation_token
    )
    return success_response(result, message="账户激活成功")


@router.post(
    "/list",
    summary="查询用户列表",
    description="""
    分页查询租户下的用户列表

    **功能说明：**
    - 支持游标分页查询，性能优异
    - 支持按用户名、邮箱、昵称进行模糊搜索
    - 支持按用户状态进行筛选
    - 支持按角色进行筛选
    - 返回用户基本信息和角色信息

    **查询参数：**
    - cursor: 分页游标，首次查询传null
    - limit: 每页数量，范围1-100，默认20
    - keyword: 搜索关键词，支持模糊匹配
    - status: 状态筛选
    - role_id: 角色筛选

    **返回数据：**
    - 用户列表：包含基本信息和角色信息
    - 分页信息：总数、下一页游标、是否有更多数据
    """,
    response_model=UserListResponseModel,
    responses={
        200: {"description": "查询成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def list_users(
    request: BaseRequest[ListUsersRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    查询用户列表

    支持分页、搜索和筛选的用户列表查询
    """
    list_users_params = request.data
    result = await user_service.list_users(
        tenant_id=list_users_params.tenant_id,
        cursor=list_users_params.cursor,
        limit=list_users_params.limit,
        keyword=list_users_params.keyword,
        status=list_users_params.status,
        role_id=list_users_params.role_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取用户详情",
    description="""
    根据用户ID获取用户详细信息

    **功能说明：**
    - 获取指定用户的完整详细信息
    - 可选择包含用户权限信息
    - 可选择包含用户会话信息
    - 包含用户安全设置信息

    **返回数据：**
    - 用户基本信息：用户名、邮箱、状态等
    - 用户角色信息：分配的角色列表
    - 用户权限信息：有效权限列表（可选）
    - 用户会话信息：活跃会话列表（可选）
    - 安全设置：MFA状态、密码过期时间等
    """,
    response_model=UserDetailResponseModel,
    responses={
        200: {"description": "查询成功"},
        404: {"description": "用户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def get_user_detail(
    request: BaseRequest[GetUserRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    获取用户详情

    根据用户ID获取完整的用户详细信息
    """
    get_user_params = request.data
    result = await user_service.get_user_detail(
        tenant_id=get_user_params.tenant_id,
        user_id=get_user_params.user_id,
        include_permissions=get_user_params.include_permissions,
        include_sessions=get_user_params.include_sessions
    )
    return success_response(result, message="查询成功")


@router.post(
    "/profile",
    summary="获取用户个人资料",
    description="""
    获取用户的个人资料和偏好设置

    **功能说明：**
    - 获取用户的个人资料信息
    - 包含用户偏好设置
    - 包含安全信息摘要
    - 通常用于个人中心页面

    **返回数据：**
    - 用户基本信息
    - 用户资料：头像、部门、职位等
    - 偏好设置：语言、时区、主题等
    - 安全信息：MFA状态、最后登录时间等
    """,
    response_model=UserProfileResponseModel,
    responses={
        200: {"description": "查询成功"},
        404: {"description": "用户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def get_user_profile(
    request: BaseRequest[GetUserProfileRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    获取用户个人资料

    获取用户的个人资料和偏好设置
    """
    get_profile_params = request.data
    result = await user_service.get_user_profile(
        tenant_id=get_profile_params.tenant_id,
        user_id=get_profile_params.user_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新用户信息",
    description="""
    更新指定用户的信息

    **功能说明：**
    - 支持部分字段更新，只传入需要修改的字段
    - 自动记录变更历史和审计日志
    - 更新后自动清理相关缓存
    - 支持用户基本信息和资料修改

    **业务规则：**
    - 用户名更新时需要保证租户内唯一性
    - 邮箱更新时需要保证租户内唯一性
    - 手机号更新时需要保证租户内唯一性
    - 状态变更需要符合业务流程规则

    **返回数据：**
    - 更新后的用户完整信息
    - 更新时间戳
    """,
    response_model=UpdateUserResponseModel,
    responses={
        200: {"description": "更新成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户不存在"},
        409: {"description": "用户名或邮箱已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def update_user(
    request: BaseRequest[UpdateUserRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    更新用户信息

    支持部分字段更新的用户信息修改
    """
    update_user_params = request.data
    result = await user_service.update_user(
        tenant_id=update_user_params.tenant_id,
        user_id=update_user_params.user_id,
        nickname=update_user_params.nickname,
        email=update_user_params.email,
        phone=update_user_params.phone,
        status=update_user_params.status,
        profile=update_user_params.profile
    )
    return success_response(result, message="用户信息更新成功")


@router.post(
    "/preferences/update",
    summary="更新用户偏好设置",
    description="""
    更新用户的个人偏好和界面设置

    **功能说明：**
    - 更新用户的个人偏好设置
    - 支持语言、时区、主题等设置
    - 支持通知偏好设置
    - 支持界面布局设置

    **支持的偏好项：**
    - language: 语言设置
    - timezone: 时区设置
    - theme: 主题设置
    - notifications: 通知设置
    - dashboard_layout: 仪表板布局

    **返回数据：**
    - 更新后的偏好设置
    - 更新时间戳
    """,
    response_model=UserProfileResponseModel,
    responses={
        200: {"description": "偏好设置更新成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def update_user_preferences(
    request: BaseRequest[UpdateUserPreferencesRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    更新用户偏好设置

    更新用户的个人偏好和界面设置
    """
    update_preferences_params = request.data
    result = await user_service.update_user_preferences(
        tenant_id=update_preferences_params.tenant_id,
        user_id=update_preferences_params.user_id,
        preferences=update_preferences_params.preferences
    )
    return success_response(result, message="偏好设置更新成功")


@router.post(
    "/status/update",
    summary="用户状态管理",
    description="""
    更新用户的状态

    **功能说明：**
    - 支持用户状态的安全变更
    - 自动验证状态转换规则
    - 支持锁定时间设置
    - 可选择发送状态变更通知
    - 记录状态变更审计日志

    **状态转换规则：**
    - pending → active, locked, deleted
    - active → locked, inactive, deleted
    - locked → active, deleted
    - inactive → active, deleted
    - deleted → 无法转换到其他状态

    **返回数据：**
    - 状态变更结果
    - 变更时间和原因
    """,
    response_model=UpdateUserStatusResponseModel,
    responses={
        200: {"description": "用户状态更新成功"},
        400: {"description": "状态转换不合法"},
        404: {"description": "用户不存在"},
        409: {"description": "不满足状态变更条件"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def update_user_status(
    request: BaseRequest[UpdateUserStatusRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    用户状态管理

    更新用户的状态，支持锁定、解锁、暂停等操作
    """
    update_status_params = request.data
    result = await user_service.update_user_status(
        tenant_id=update_status_params.tenant_id,
        user_id=update_status_params.user_id,
        status=update_status_params.status,
        reason=update_status_params.reason,
        locked_until=update_status_params.locked_until,
        send_notification=update_status_params.send_notification
    )
    return success_response(result, message="用户状态更新成功")


@router.post(
    "/delete",
    summary="删除用户",
    description="""
    删除指定用户

    **功能说明：**
    - 支持软删除和硬删除两种模式
    - 软删除：标记为已删除状态，保留数据便于恢复
    - 硬删除：物理删除数据，不可恢复
    - 支持数据转移到其他用户
    - 需要确认码防止误删除
    - 自动终止用户的所有会话

    **业务规则：**
    - 默认为软删除模式，安全性更高
    - 需要提供确认码验证
    - 可选择将用户数据转移给其他用户
    - 删除操作会记录详细的审计日志

    **返回数据：**
    - 删除操作结果和状态
    - 数据转移信息
    - 终止的会话数量
    """,
    response_model=DeleteUserResponseModel,
    responses={
        200: {"description": "用户删除成功"},
        400: {"description": "请求参数错误或确认码无效"},
        404: {"description": "用户不存在"},
        409: {"description": "存在依赖关系，无法删除"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户管理"]
)
@inject
async def delete_user(
    request: BaseRequest[DeleteUserRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    删除用户

    支持软删除和硬删除的用户删除操作
    """
    delete_user_params = request.data
    result = await user_service.delete_user(
        tenant_id=delete_user_params.tenant_id,
        user_id=delete_user_params.user_id,
        delete_type=delete_user_params.delete_type,
        reason=delete_user_params.reason,
        transfer_data_to=delete_user_params.transfer_data_to,
        confirmation_code=delete_user_params.confirmation_code
    )
    return success_response(result, message="用户删除成功")


@router.post(
    "/change_phone",
    summary="更换手机号",
    description="""
    更换用户手机号

    **功能说明：**
    - 需要短信验证码验证
    - 支持新旧手机号验证
    - 自动更新用户信息
    - 记录变更审计日志

    **业务规则：**
    - 验证码必须有效且未过期
    - 新手机号在租户内唯一
    - 需要验证原手机号（如果存在）

    **返回数据：**
    - 更换结果（脱敏显示）
    - 更换时间
    """,
    response_model=ChangeContactResponseModel,
    responses={
        200: {"description": "手机号更换成功"},
        400: {"description": "验证码无效或手机号格式错误"},
        404: {"description": "用户不存在"},
        409: {"description": "手机号已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户安全"]
)
@inject
async def change_phone(
    request: BaseRequest[ChangePhoneRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    更换手机号

    需要短信验证码验证的手机号更换
    """
    change_phone_params = request.data
    result = await user_service.change_phone(
        tenant_id=change_phone_params.tenant_id,
        user_id=change_phone_params.user_id,
        old_phone=change_phone_params.old_phone,
        new_phone=change_phone_params.new_phone,
        sms_code=change_phone_params.sms_code,
        code_id=change_phone_params.code_id
    )
    return success_response(result, message="手机号更换成功")


@router.post(
    "/change_email",
    summary="更换邮箱",
    description="""
    更换用户邮箱地址

    **功能说明：**
    - 需要邮箱验证码验证
    - 支持新旧邮箱验证
    - 自动更新用户信息
    - 记录变更审计日志

    **业务规则：**
    - 验证码必须有效且未过期
    - 新邮箱在租户内唯一
    - 需要验证原邮箱

    **返回数据：**
    - 更换结果（脱敏显示）
    - 更换时间
    """,
    response_model=ChangeContactResponseModel,
    responses={
        200: {"description": "邮箱更换成功"},
        400: {"description": "验证码无效或邮箱格式错误"},
        404: {"description": "用户不存在"},
        409: {"description": "邮箱已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户安全"]
)
@inject
async def change_email(
    request: BaseRequest[ChangeEmailRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    更换邮箱

    需要邮箱验证码验证的邮箱更换
    """
    change_email_params = request.data
    result = await user_service.change_email(
        tenant_id=change_email_params.tenant_id,
        user_id=change_email_params.user_id,
        old_email=change_email_params.old_email,
        new_email=change_email_params.new_email,
        email_code=change_email_params.email_code,
        code_id=change_email_params.code_id
    )
    return success_response(result, message="邮箱更换成功")


@router.post(
    "/send_sms_code",
    summary="发送短信验证码",
    description="""
    发送短信验证码

    **功能说明：**
    - 支持多种场景的短信验证码发送
    - 验证码有效期管理
    - 发送频率限制
    - 自动记录发送日志

    **支持场景：**
    - register: 用户注册
    - change_phone: 更换手机号
    - reset_password: 重置密码
    - login: 登录验证

    **业务规则：**
    - 同一手机号发送间隔限制（通常60秒）
    - 每日发送次数限制
    - 验证码有效期（通常5分钟）

    **返回数据：**
    - 发送目标（脱敏显示）
    - 验证码ID
    - 过期时间
    """,
    response_model=SendCodeResponseModel,
    responses={
        200: {"description": "短信验证码发送成功"},
        400: {"description": "请求参数错误或发送过于频繁"},
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户安全"]
)
@inject
async def send_sms_code(
    request: BaseRequest[SendSmsCodeRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    发送短信验证码

    支持多种场景的短信验证码发送
    """
    send_sms_params = request.data
    result = await user_service.send_sms_code(
        tenant_id=send_sms_params.tenant_id,
        phone=send_sms_params.phone,
        scene=send_sms_params.scene,
        user_id=send_sms_params.user_id
    )
    return success_response(result, message="短信验证码发送成功")


@router.post(
    "/send_email_code",
    summary="发送邮箱验证码",
    description="""
    发送邮箱验证码

    **功能说明：**
    - 支持多种场景的邮箱验证码发送
    - 验证码有效期管理
    - 发送频率限制
    - 自动记录发送日志

    **支持场景：**
    - register: 用户注册
    - change_email: 更换邮箱
    - reset_password: 重置密码
    - login: 登录验证

    **业务规则：**
    - 同一邮箱发送间隔限制（通常60秒）
    - 每日发送次数限制
    - 验证码有效期（通常5分钟）

    **返回数据：**
    - 发送目标（脱敏显示）
    - 验证码ID
    - 过期时间
    """,
    response_model=SendCodeResponseModel,
    responses={
        200: {"description": "邮箱验证码发送成功"},
        400: {"description": "请求参数错误或发送过于频繁"},
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户安全"]
)
@inject
async def send_email_code(
    request: BaseRequest[SendEmailCodeRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    发送邮箱验证码

    支持多种场景的邮箱验证码发送
    """
    send_email_params = request.data
    result = await user_service.send_email_code(
        tenant_id=send_email_params.tenant_id,
        email=send_email_params.email,
        scene=send_email_params.scene,
        user_id=send_email_params.user_id
    )
    return success_response(result, message="邮箱验证码发送成功")


# ===== TODO: 未完成的功能清单 =====
"""
以下功能尚未实现，需要在后续开发中完成：

1. 批量操作功能：
   - 批量用户导入 (POST /users/batch_import)
   - 批量用户导出 (POST /users/batch_export)
   - 批量状态更新 (POST /users/batch_status_update)
   - 批量角色分配 (POST /users/batch_assign_roles)

2. 用户角色管理功能：
   - 为用户分配角色 (POST /users/assign_roles)
   - 移除用户角色 (POST /users/remove_roles)
   - 查询用户角色历史 (POST /users/role_history)

3. 用户权限查询功能：
   - 查询用户有效权限 (POST /users/permissions)
   - 查询用户权限来源 (POST /users/permission_sources)
   - 权限继承关系查询 (POST /users/permission_inheritance)

4. 用户会话管理功能：
   - 查询用户活跃会话 (POST /users/sessions)
   - 强制下线指定会话 (POST /users/sessions/terminate)
   - 强制下线所有会话 (POST /users/sessions/terminate_all)

5. 用户统计分析功能：
   - 用户活跃度统计 (POST /users/statistics/activity)
   - 用户登录统计 (POST /users/statistics/login)
   - 用户行为分析 (POST /users/statistics/behavior)

6. 用户数据导出功能：
   - 导出用户基本信息 (POST /users/export/basic)
   - 导出用户详细信息 (POST /users/export/detailed)
   - 导出用户操作日志 (POST /users/export/audit_logs)

7. 用户通知功能：
   - 发送用户通知 (POST /users/notifications/send)
   - 查询用户通知历史 (POST /users/notifications/history)
   - 更新通知状态 (POST /users/notifications/update_status)

注意：这些功能需要与其他模块（如角色管理、权限管理、认证模块等）协同开发。
"""





@router.post(
    "/assign_roles",
    summary="分配角色",
    description="""
    为用户分配一个或多个角色

    **功能说明：**
    - 支持为用户分配多个角色
    - 支持永久分配和临时分配
    - 自动验证角色有效性和权限
    - 记录角色分配审计日志
    - 支持批量分配操作

    **业务规则：**
    - 角色必须存在且处于活跃状态
    - 用户不能重复分配相同角色
    - 临时分配需要设置过期时间
    - 需要有角色分配权限

    **返回数据：**
    - 成功分配的角色列表
    - 分配失败的角色及原因
    - 分配时间和类型
    """,
    response_model=AssignRolesResponseModel,
    responses={
        200: {
            "description": "角色分配成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "角色分配成功",
                        "data": {
                            "user_id": "user_550e8400-e29b-41d4-a716-************",
                            "assigned_roles": [
                                {
                                    "role_id": "role_550e8400-e29b-41d4-a716-446655440001",
                                    "role_name": "管理员",
                                    "role_code": "ADMIN",
                                    "assigned_at": "2025-01-15T10:30:45.123456"
                                }
                            ],
                            "failed_roles": [],
                            "assigned_at": "2025-01-15T10:30:45.123456"
                        }
                    }
                }
            }
        },
        400: {"description": "请求参数错误"},
        404: {"description": "用户或角色不存在"},
        409: {"description": "角色已分配或权限不足"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户角色管理"]
)
@inject
async def assign_roles(
    request: BaseRequest[AssignRolesRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    分配角色

    为用户分配一个或多个角色，支持永久和临时分配
    """
    assign_roles_params = request.data
    result = await user_service.assign_roles(
        user_id=assign_roles_params.user_id,
        tenant_id=assign_roles_params.tenant_id,
        role_ids=assign_roles_params.role_ids,
        assignment_type=assign_roles_params.assignment_type,
        expires_at=assign_roles_params.expires_at,
        reason=assign_roles_params.reason
    )
    return success_response(result, message="角色分配成功")


@router.post(
    "/remove_roles",
    summary="移除角色",
    description="""
    移除用户的一个或多个角色

    **功能说明：**
    - 支持移除用户的多个角色
    - 自动验证角色移除的合法性
    - 检查角色依赖关系
    - 记录角色移除审计日志
    - 支持强制移除模式

    **业务规则：**
    - 用户必须拥有要移除的角色
    - 不能移除用户的最后一个角色（除非强制）
    - 检查角色依赖关系
    - 需要有角色管理权限

    **返回数据：**
    - 成功移除的角色列表
    - 移除失败的角色及原因
    - 移除后剩余的角色列表
    - 移除时间
    """,
    response_model=RemoveRolesResponseModel,
    responses={
        200: {
            "description": "角色移除成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "角色移除成功",
                        "data": {
                            "user_id": "user_550e8400-e29b-41d4-a716-************",
                            "removed_roles": ["role_550e8400-e29b-41d4-a716-446655440001"],
                            "failed_roles": [],
                            "remaining_roles": [
                                {
                                    "role_id": "role_550e8400-e29b-41d4-a716-446655440002",
                                    "role_name": "普通用户",
                                    "role_code": "USER",
                                    "assigned_at": "2025-01-10T10:30:45.123456"
                                }
                            ],
                            "removed_at": "2025-01-15T10:30:45.123456"
                        }
                    }
                }
            }
        },
        400: {"description": "请求参数错误"},
        404: {"description": "用户或角色不存在"},
        409: {"description": "角色未分配或存在依赖关系"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户角色管理"]
)
@inject
async def remove_roles(
    request: BaseRequest[RemoveRolesRequest],
    user_service: UserService = Depends(Provide[ServiceContainer.user_service])
):
    """
    移除角色

    移除用户的一个或多个角色，支持依赖检查和强制移除
    """
    remove_roles_params = request.data
    result = await user_service.remove_roles(
        user_id=remove_roles_params.user_id,
        tenant_id=remove_roles_params.tenant_id,
        role_ids=remove_roles_params.role_ids,
        reason=remove_roles_params.reason,
        force_remove=remove_roles_params.force_remove
    )
    return success_response(result, message="角色移除成功")


