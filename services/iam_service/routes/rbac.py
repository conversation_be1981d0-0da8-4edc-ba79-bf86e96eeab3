"""
RBAC (基于角色的访问控制) 路由

提供角色和权限管理的API接口，支持RBAC权限模型
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel, Field
from dependency_injector.wiring import inject, Provide

from commonlib.schemas.request import BaseRequest
from pydantic import BaseModel
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.rbac_service import RBACService

router = APIRouter(prefix="/rbac", tags=["角色权限管理"])


# ===== 请求数据模型 =====

class CreateRoleRequestData(BaseModel):
    """
    创建角色请求数据模型

    管理员创建新角色，定义角色的基本信息和层级关系
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    role_name: str = Field(
        ...,
        description="角色名称",
        min_length=2,
        max_length=50,
        examples=["管理员"]
    )
    role_code: str = Field(
        ...,
        description="角色代码",
        min_length=2,
        max_length=50,
        pattern="^[A-Z_][A-Z0-9_]*$",
        examples=["ADMIN"]
    )
    description: Optional[str] = Field(
        None,
        description="角色描述",
        max_length=200,
        examples=["系统管理员角色"]
    )
    parent_role_id: Optional[str] = Field(
        None,
        description="父角色ID（用于角色继承）",
        examples=["role_550e8400-e29b-41d4-a716-446655440000"]
    )
    level: int = Field(
        1,
        description="角色层级（1为最高级）",
        ge=1,
        le=10,
        examples=[1]
    )
    is_system: bool = Field(
        False,
        description="是否为系统角色（系统角色不可删除）",
        examples=[False]
    )
    permissions: List[str] = Field(
        default_factory=list,
        description="角色权限列表",
        examples=[["user:read", "user:write", "role:read"]]
    )


class UpdateRoleRequestData(BaseModel):
    """
    更新角色请求数据模型

    更新角色的基本信息，不包括权限分配
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    role_id: str = Field(
        ...,
        description="角色ID",
        examples=["role_550e8400-e29b-41d4-a716-446655440000"]
    )
    role_name: Optional[str] = Field(
        None,
        description="角色名称",
        min_length=2,
        max_length=50,
        examples=["高级管理员"]
    )
    description: Optional[str] = Field(
        None,
        description="角色描述",
        max_length=200,
        examples=["高级系统管理员角色"]
    )
    parent_role_id: Optional[str] = Field(
        None,
        description="父角色ID",
        examples=["role_550e8400-e29b-41d4-a716-446655440000"]
    )
    level: Optional[int] = Field(
        None,
        description="角色层级",
        ge=1,
        le=10,
        examples=[2]
    )


class QueryRolesRequestData(BaseModel):
    """
    查询角色列表请求数据模型

    支持多条件查询和分页
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    role_name: Optional[str] = Field(
        None,
        description="角色名称（模糊查询）",
        examples=["管理"]
    )
    parent_role_id: Optional[str] = Field(
        None,
        description="父角色ID",
        examples=["role_550e8400-e29b-41d4-a716-446655440000"]
    )
    level: Optional[int] = Field(
        None,
        description="角色层级",
        ge=1,
        le=10,
        examples=[1]
    )
    include_permissions: bool = Field(
        False,
        description="是否包含权限信息",
        examples=[True]
    )
    include_users: bool = Field(
        False,
        description="是否包含用户数量",
        examples=[False]
    )
    page: int = Field(
        1,
        description="页码",
        ge=1,
        examples=[1]
    )
    page_size: int = Field(
        20,
        description="每页数量",
        ge=1,
        le=100,
        examples=[20]
    )


class DeleteRoleRequestData(BaseModel):
    """
    删除角色请求数据模型

    删除指定角色，支持强制删除选项
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    role_id: str = Field(
        ...,
        description="角色ID",
        examples=["role_550e8400-e29b-41d4-a716-446655440000"]
    )
    force_delete: bool = Field(
        False,
        description="是否强制删除（即使有用户关联）",
        examples=[False]
    )
    transfer_to_role_id: Optional[str] = Field(
        None,
        description="转移用户到的目标角色ID",
        examples=["role_550e8400-e29b-41d4-a716-446655440001"]
    )


class CreatePermissionRequestData(BaseModel):
    """
    创建权限请求数据模型

    定义系统权限，支持资源和操作的组合
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    permission_code: str = Field(
        ...,
        description="权限代码",
        min_length=3,
        max_length=100,
        pattern="^[a-z][a-z0-9_]*:[a-z][a-z0-9_]*$",
        examples=["user:read"]
    )
    permission_name: str = Field(
        ...,
        description="权限名称",
        min_length=2,
        max_length=50,
        examples=["查看用户"]
    )
    description: Optional[str] = Field(
        None,
        description="权限描述",
        max_length=200,
        examples=["查看用户基本信息的权限"]
    )
    resource_type: str = Field(
        ...,
        description="资源类型",
        examples=["user"]
    )
    action: str = Field(
        ...,
        description="操作类型",
        examples=["read"]
    )
    category: str = Field(
        "business",
        description="权限分类：system(系统)、business(业务)、data(数据)",
        examples=["business"]
    )
    is_system: bool = Field(
        False,
        description="是否为系统权限（系统权限不可删除）",
        examples=[False]
    )


class AssignRolePermissionsRequestData(BaseModel):
    """
    分配角色权限请求数据模型

    为角色分配或移除权限
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    role_id: str = Field(
        ...,
        description="角色ID",
        examples=["role_550e8400-e29b-41d4-a716-446655440000"]
    )
    permission_codes: List[str] = Field(
        ...,
        description="权限代码列表",
        examples=[["user:read", "user:write", "role:read"]]
    )
    operation: str = Field(
        "assign",
        description="操作类型：assign(分配)、remove(移除)、replace(替换)",
        examples=["assign"]
    )


class AssignUserRolesRequestData(BaseModel):
    """
    分配用户角色请求数据模型

    为用户分配或移除角色
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    role_ids: List[str] = Field(
        ...,
        description="角色ID列表",
        examples=[["role_550e8400-e29b-41d4-a716-446655440000"]]
    )
    operation: str = Field(
        "assign",
        description="操作类型：assign(分配)、remove(移除)、replace(替换)",
        examples=["assign"]
    )
    effective_time: Optional[str] = Field(
        None,
        description="生效时间（ISO格式）",
        examples=["2025-01-15T10:30:45.123456"]
    )
    expire_time: Optional[str] = Field(
        None,
        description="过期时间（ISO格式）",
        examples=["2025-12-31T23:59:59.999999"]
    )


class CheckPermissionRequestData(BaseModel):
    """
    权限检查请求数据模型

    检查用户是否具有指定权限
    """
    tenant_id: str = Field(
        ...,
        description="租户ID",
        examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: str = Field(
        ...,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"]
    )
    permission_code: str = Field(
        ...,
        description="权限代码",
        examples=["user:read"]
    )
    resource_id: Optional[str] = Field(
        None,
        description="资源ID（用于数据级权限控制）",
        examples=["user_550e8400-e29b-41d4-a716-446655440001"]
    )
    context: Optional[Dict[str, Any]] = Field(
        None,
        description="权限检查上下文",
        examples=[{"department": "IT", "level": "manager"}]
    )


# ===== 响应数据模型 =====

class RoleInfo(BaseModel):
    """角色信息模型"""
    role_id: str = Field(..., description="角色ID", examples=["role_550e8400-e29b-41d4-a716-446655440000"])
    role_name: str = Field(..., description="角色名称", examples=["管理员"])
    role_code: str = Field(..., description="角色代码", examples=["ADMIN"])
    description: Optional[str] = Field(None, description="角色描述", examples=["系统管理员角色"])
    parent_role_id: Optional[str] = Field(None, description="父角色ID")
    level: int = Field(..., description="角色层级", examples=[1])
    is_system: bool = Field(..., description="是否为系统角色", examples=[False])
    status: str = Field(..., description="角色状态", examples=["active"])
    created_at: str = Field(..., description="创建时间", examples=["2025-01-15T10:30:45.123456"])
    updated_at: Optional[str] = Field(None, description="更新时间")
    permissions: Optional[List[str]] = Field(None, description="权限列表")
    user_count: Optional[int] = Field(None, description="关联用户数量")


class PermissionInfo(BaseModel):
    """权限信息模型"""
    permission_id: str = Field(..., description="权限ID", examples=["perm_550e8400-e29b-41d4-a716-446655440000"])
    permission_code: str = Field(..., description="权限代码", examples=["user:read"])
    permission_name: str = Field(..., description="权限名称", examples=["查看用户"])
    description: Optional[str] = Field(None, description="权限描述")
    resource_type: str = Field(..., description="资源类型", examples=["user"])
    action: str = Field(..., description="操作类型", examples=["read"])
    category: str = Field(..., description="权限分类", examples=["business"])
    is_system: bool = Field(..., description="是否为系统权限", examples=[False])
    created_at: str = Field(..., description="创建时间")


class UserRoleInfo(BaseModel):
    """用户角色关联信息模型"""
    user_id: str = Field(..., description="用户ID")
    role_id: str = Field(..., description="角色ID")
    role_name: str = Field(..., description="角色名称")
    assigned_at: str = Field(..., description="分配时间")
    assigned_by: str = Field(..., description="分配人")
    effective_time: Optional[str] = Field(None, description="生效时间")
    expire_time: Optional[str] = Field(None, description="过期时间")
    is_active: bool = Field(..., description="是否有效", examples=[True])


class CreateRoleResponse(BaseModel):
    """创建角色响应数据"""
    role_id: str = Field(..., description="角色ID")
    role_name: str = Field(..., description="角色名称")
    role_code: str = Field(..., description="角色代码")
    created_at: str = Field(..., description="创建时间")
    permissions_assigned: int = Field(..., description="分配的权限数量")


class QueryRolesResponse(BaseModel):
    """查询角色列表响应数据"""
    roles: List[RoleInfo] = Field(..., description="角色列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")


class CreatePermissionResponse(BaseModel):
    """创建权限响应数据"""
    permission_id: str = Field(..., description="权限ID")
    permission_code: str = Field(..., description="权限代码")
    permission_name: str = Field(..., description="权限名称")
    created_at: str = Field(..., description="创建时间")


class AssignRolePermissionsResponse(BaseModel):
    """分配角色权限响应数据"""
    role_id: str = Field(..., description="角色ID")
    operation: str = Field(..., description="操作类型")
    affected_permissions: int = Field(..., description="影响的权限数量")
    current_permissions: List[str] = Field(..., description="当前权限列表")
    operation_time: str = Field(..., description="操作时间")


class AssignUserRolesResponse(BaseModel):
    """分配用户角色响应数据"""
    user_id: str = Field(..., description="用户ID")
    operation: str = Field(..., description="操作类型")
    affected_roles: int = Field(..., description="影响的角色数量")
    current_roles: List[str] = Field(..., description="当前角色列表")
    operation_time: str = Field(..., description="操作时间")


class CheckPermissionResponse(BaseModel):
    """权限检查响应数据"""
    user_id: str = Field(..., description="用户ID")
    permission_code: str = Field(..., description="权限代码")
    has_permission: bool = Field(..., description="是否具有权限")
    permission_source: Optional[str] = Field(None, description="权限来源（角色名称）")
    checked_at: str = Field(..., description="检查时间")
    cache_hit: bool = Field(..., description="是否命中缓存")


class DeleteRoleResponse(BaseModel):
    """删除角色响应数据"""
    role_id: str = Field(..., description="被删除的角色ID")
    affected_users: int = Field(..., description="影响的用户数量")
    transferred_to: Optional[str] = Field(None, description="用户转移到的角色ID")
    deleted_at: str = Field(..., description="删除时间")


# ===== 响应模型包装类 =====

class CreateRoleResponseModel(SuccessResponse[CreateRoleResponse]):
    """创建角色响应模型"""
    data: CreateRoleResponse = Field(..., description="创建角色结果数据")


class QueryRolesResponseModel(SuccessResponse[QueryRolesResponse]):
    """查询角色列表响应模型"""
    data: QueryRolesResponse = Field(..., description="角色列表数据")


class CreatePermissionResponseModel(SuccessResponse[CreatePermissionResponse]):
    """创建权限响应模型"""
    data: CreatePermissionResponse = Field(..., description="创建权限结果数据")


class AssignRolePermissionsResponseModel(SuccessResponse[AssignRolePermissionsResponse]):
    """分配角色权限响应模型"""
    data: AssignRolePermissionsResponse = Field(..., description="分配权限结果数据")


class AssignUserRolesResponseModel(SuccessResponse[AssignUserRolesResponse]):
    """分配用户角色响应模型"""
    data: AssignUserRolesResponse = Field(..., description="分配角色结果数据")


class CheckPermissionResponseModel(SuccessResponse[CheckPermissionResponse]):
    """权限检查响应模型"""
    data: CheckPermissionResponse = Field(..., description="权限检查结果数据")


class DeleteRoleResponseModel(SuccessResponse[DeleteRoleResponse]):
    """删除角色响应模型"""
    data: DeleteRoleResponse = Field(..., description="删除角色结果数据")


# ===== 路由端点 =====

@router.post(
    "/roles/create",
    summary="创建角色",
    description="""
    创建新角色

    **功能说明：**
    - 创建新的角色并分配权限
    - 支持角色层级和继承关系
    - 自动验证角色代码唯一性
    - 支持系统角色标记

    **业务规则：**
    - 角色代码在租户内必须唯一
    - 角色层级必须合理（子角色层级 > 父角色层级）
    - 系统角色具有特殊保护

    **返回数据：**
    - 角色基本信息
    - 分配的权限数量
    - 创建时间
    """,
    response_model=CreateRoleResponseModel,
    responses={
        200: {"description": "角色创建成功"},
        400: {"description": "请求参数错误"},
        409: {"description": "角色代码已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["角色管理"]
)
@inject
async def create_role(
        request: BaseRequest[CreateRoleRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    创建角色

    创建新的角色并设置基本信息和权限
    """
    role_params = request.data
    result = await rbac_service.create_role(
        tenant_id=role_params.tenant_id,
        role_name=role_params.role_name,
        role_code=role_params.role_code,
        description=role_params.description,
        parent_role_id=role_params.parent_role_id,
        level=role_params.level,
        is_system=role_params.is_system,
        permissions=role_params.permissions
    )
    return success_response(result, message="角色创建成功")


@router.post(
    "/roles/query",
    summary="查询角色列表",
    description="""
    查询角色列表

    **功能说明：**
    - 支持多条件查询和分页
    - 可选择包含权限和用户信息
    - 支持角色层级过滤
    - 支持模糊查询

    **查询条件：**
    - 角色名称模糊查询
    - 父角色ID过滤
    - 角色层级过滤
    - 分页参数

    **返回数据：**
    - 角色列表（含权限信息）
    - 分页信息
    - 用户数量统计
    """,
    response_model=QueryRolesResponseModel,
    responses={
        200: {"description": "查询成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"}
    },
    tags=["角色管理"]
)
@inject
async def query_roles(
        request: BaseRequest[QueryRolesRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    查询角色列表

    支持多条件查询和分页的角色列表查询
    """
    query_params = request.data
    result = await rbac_service.query_roles(
        tenant_id=query_params.tenant_id,
        role_name=query_params.role_name,
        parent_role_id=query_params.parent_role_id,
        level=query_params.level,
        include_permissions=query_params.include_permissions,
        include_users=query_params.include_users,
        page=query_params.page,
        page_size=query_params.page_size
    )
    return success_response(result, message="查询成功")


@router.post(
    "/roles/update",
    summary="更新角色",
    description="""
    更新角色信息

    **功能说明：**
    - 更新角色基本信息
    - 支持层级关系调整
    - 保护系统角色
    - 验证层级合理性

    **业务规则：**
    - 系统角色的某些属性不可修改
    - 层级调整需要验证合理性
    - 不能形成循环继承

    **返回数据：**
    - 更新后的角色信息
    - 更新时间
    """,
    response_model=CreateRoleResponseModel,
    responses={
        200: {"description": "角色更新成功"},
        400: {"description": "请求参数错误"},
        403: {"description": "系统角色不可修改"},
        404: {"description": "角色不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["角色管理"]
)
@inject
async def update_role(
        request: BaseRequest[UpdateRoleRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    更新角色

    更新角色的基本信息和层级关系
    """
    update_params = request.data
    result = await rbac_service.update_role(
        tenant_id=update_params.tenant_id,
        role_id=update_params.role_id,
        role_name=update_params.role_name,
        description=update_params.description,
        parent_role_id=update_params.parent_role_id,
        level=update_params.level
    )
    return success_response(result, message="角色更新成功")


@router.post(
    "/roles/delete",
    summary="删除角色",
    description="""
    删除角色

    **功能说明：**
    - 删除指定角色
    - 支持用户转移到其他角色
    - 保护系统角色和有用户关联的角色
    - 支持强制删除选项

    **业务规则：**
    - 系统角色不可删除
    - 有用户关联的角色需要转移或强制删除
    - 删除后清理相关权限关联

    **返回数据：**
    - 删除结果
    - 影响的用户数量
    - 用户转移信息
    """,
    response_model=DeleteRoleResponseModel,
    responses={
        200: {"description": "角色删除成功"},
        400: {"description": "请求参数错误"},
        403: {"description": "系统角色不可删除"},
        404: {"description": "角色不存在"},
        409: {"description": "角色有用户关联"},
        500: {"description": "服务器内部错误"}
    },
    tags=["角色管理"]
)
@inject
async def delete_role(
        request: BaseRequest[DeleteRoleRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    删除角色

    删除指定角色并处理用户关联
    """
    delete_params = request.data
    result = await rbac_service.delete_role(
        tenant_id=delete_params.tenant_id,
        role_id=delete_params.role_id,
        force_delete=delete_params.force_delete,
        transfer_to_role_id=delete_params.transfer_to_role_id
    )
    return success_response(result, message="角色删除成功")


@router.post(
    "/permissions/create",
    summary="创建权限",
    description="""
    创建新权限

    **功能说明：**
    - 定义系统权限
    - 支持资源和操作的组合
    - 权限分类管理
    - 系统权限保护

    **权限格式：**
    - 权限代码格式：resource:action
    - 例如：user:read, role:write, document:delete

    **返回数据：**
    - 权限基本信息
    - 创建时间
    """,
    response_model=CreatePermissionResponseModel,
    responses={
        200: {"description": "权限创建成功"},
        400: {"description": "请求参数错误"},
        409: {"description": "权限代码已存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["权限管理"]
)
@inject
async def create_permission(
        request: BaseRequest[CreatePermissionRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    创建权限

    创建新的系统权限定义
    """
    perm_params = request.data
    result = await rbac_service.create_permission(
        tenant_id=perm_params.tenant_id,
        permission_code=perm_params.permission_code,
        permission_name=perm_params.permission_name,
        description=perm_params.description,
        resource_type=perm_params.resource_type,
        action=perm_params.action,
        category=perm_params.category,
        is_system=perm_params.is_system
    )
    return success_response(result, message="权限创建成功")


@router.post(
    "/roles/permissions/assign",
    summary="分配角色权限",
    description="""
    分配角色权限

    **功能说明：**
    - 为角色分配或移除权限
    - 支持批量操作
    - 支持权限替换
    - 自动更新权限缓存

    **操作类型：**
    - assign：分配权限（追加）
    - remove：移除权限
    - replace：替换权限（清空后重新分配）

    **返回数据：**
    - 操作结果
    - 影响的权限数量
    - 当前权限列表
    """,
    response_model=AssignRolePermissionsResponseModel,
    responses={
        200: {"description": "权限分配成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "角色或权限不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["权限管理"]
)
@inject
async def assign_role_permissions(
        request: BaseRequest[AssignRolePermissionsRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    分配角色权限

    为指定角色分配、移除或替换权限
    """
    assign_params = request.data
    result = await rbac_service.assign_role_permissions(
        tenant_id=assign_params.tenant_id,
        role_id=assign_params.role_id,
        permission_codes=assign_params.permission_codes,
        operation=assign_params.operation
    )
    return success_response(result, message="权限分配成功")


@router.post(
    "/users/roles/assign",
    summary="分配用户角色",
    description="""
    分配用户角色

    **功能说明：**
    - 为用户分配或移除角色
    - 支持角色生效和过期时间
    - 支持批量操作
    - 自动更新用户权限缓存

    **操作类型：**
    - assign：分配角色（追加）
    - remove：移除角色
    - replace：替换角色（清空后重新分配）

    **返回数据：**
    - 操作结果
    - 影响的角色数量
    - 当前角色列表
    """,
    response_model=AssignUserRolesResponseModel,
    responses={
        200: {"description": "角色分配成功"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户或角色不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["用户角色管理"]
)
@inject
async def assign_user_roles(
        request: BaseRequest[AssignUserRolesRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    分配用户角色

    为指定用户分配、移除或替换角色
    """
    assign_params = request.data
    result = await rbac_service.assign_user_roles(
        tenant_id=assign_params.tenant_id,
        user_id=assign_params.user_id,
        role_ids=assign_params.role_ids,
        operation=assign_params.operation,
        effective_time=assign_params.effective_time,
        expire_time=assign_params.expire_time
    )
    return success_response(result, message="角色分配成功")


@router.post(
    "/permissions/check",
    summary="权限检查",
    description="""
    检查用户权限

    **功能说明：**
    - 检查用户是否具有指定权限
    - 支持数据级权限控制
    - 支持上下文权限检查
    - 权限缓存优化

    **检查逻辑：**
    - 直接权限检查
    - 角色继承权限检查
    - 数据级权限检查
    - 上下文权限检查

    **返回数据：**
    - 权限检查结果
    - 权限来源信息
    - 缓存命中状态
    """,
    response_model=CheckPermissionResponseModel,
    responses={
        200: {"description": "权限检查完成"},
        400: {"description": "请求参数错误"},
        404: {"description": "用户或权限不存在"},
        500: {"description": "服务器内部错误"}
    },
    tags=["权限验证"]
)
@inject
async def check_permission(
        request: BaseRequest[CheckPermissionRequestData],
        rbac_service: RBACService = Depends(Provide[ServiceContainer.rbac_service])
):
    """
    权限检查

    检查指定用户是否具有指定权限
    """
    check_params = request.data
    result = await rbac_service.check_permission(
        tenant_id=check_params.tenant_id,
        user_id=check_params.user_id,
        permission_code=check_params.permission_code,
        resource_id=check_params.resource_id,
        context=check_params.context
    )
    return success_response(result, message="权限检查完成")
