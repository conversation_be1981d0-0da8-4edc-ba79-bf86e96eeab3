"""
角色管理路由

提供角色的创建、查询、更新、删除等接口
"""

from fastapi import APIRouter, Depends
from dependency_injector.wiring import inject, Provide
from pydantic import BaseModel, Field
from typing import Optional, List

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from container import ServiceContainer
from services.role_service import RoleService

router = APIRouter()


# ===== 请求模型 =====
class CreateRoleRequest(BaseModel):
    """创建角色请求"""
    role_name: str = Field(..., description="角色名称", min_length=2, max_length=100)
    role_code: str = Field(..., description="角色编码", min_length=2, max_length=50)
    description: Optional[str] = Field(None, description="角色描述")
    tenant_id: str = Field(..., description="租户ID")
    permission_ids: Optional[List[str]] = Field([], description="权限ID列表")


class ListRolesRequest(BaseModel):
    """查询角色列表请求"""
    tenant_id: str = Field(..., description="租户ID")
    cursor: Optional[str] = Field(None, description="分页游标")
    limit: int = Field(20, description="每页数量", ge=1, le=100)
    search: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[str] = Field(None, description="角色状态筛选")


class GetRoleRequest(BaseModel):
    """获取角色详情请求"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")


class UpdateRoleRequest(BaseModel):
    """更新角色请求"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    role_name: Optional[str] = Field(None, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    status: Optional[str] = Field(None, description="角色状态")


class DeleteRoleRequest(BaseModel):
    """删除角色请求"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    force: bool = Field(False, description="是否强制删除")


class AssignPermissionsRequest(BaseModel):
    """分配权限请求"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    permission_ids: List[str] = Field(..., description="权限ID列表")


class RemovePermissionsRequest(BaseModel):
    """移除权限请求"""
    role_id: str = Field(..., description="角色ID")
    tenant_id: str = Field(..., description="租户ID")
    permission_ids: List[str] = Field(..., description="要移除的权限ID列表")


# ===== 响应数据模型 =====
class RoleResponse(BaseModel):
    """角色响应数据"""
    role_id: str = Field(..., description="角色ID")
    role_name: str = Field(..., description="角色名称")
    role_code: str = Field(..., description="角色编码")
    description: Optional[str] = Field(None, description="角色描述")
    status: str = Field(..., description="角色状态")
    tenant_id: str = Field(..., description="租户ID")
    permissions: List[dict] = Field([], description="角色权限列表")
    user_count: int = Field(0, description="拥有此角色的用户数量")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class RoleListResponse(BaseModel):
    """角色列表响应数据"""
    roles: List[RoleResponse] = Field(..., description="角色列表")
    total: int = Field(..., description="总数量")
    next_cursor: Optional[str] = Field(None, description="下一页游标")
    has_more: bool = Field(..., description="是否有更多数据")


class RoleOperationResponse(BaseModel):
    """角色操作响应数据"""
    success: bool = Field(True, description="操作是否成功")
    role_id: Optional[str] = Field(None, description="角色ID")


# ===== 响应模型 =====
class RoleResponseModel(SuccessResponse[RoleResponse]):
    """角色响应模型"""
    data: RoleResponse


class RoleListResponseModel(SuccessResponse[RoleListResponse]):
    """角色列表响应模型"""
    data: RoleListResponse


class RoleOperationResponseModel(SuccessResponse[RoleOperationResponse]):
    """角色操作响应模型"""
    data: RoleOperationResponse


# ===== 路由端点 =====
@router.post(
    "/create",
    summary="创建角色",
    description="在指定租户下创建新角色",
    response_model=RoleResponseModel,
)
@inject
async def create_role(
    request: BaseRequest[CreateRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """创建角色"""
    create_role_params = request.data
    result = await role_service.create_role(
        role_name=create_role_params.role_name,
        role_code=create_role_params.role_code,
        description=create_role_params.description,
        tenant_id=create_role_params.tenant_id,
        permission_ids=create_role_params.permission_ids
    )
    return success_response(result, message="角色创建成功")


@router.post(
    "/list",
    summary="查询角色列表",
    description="分页查询租户下的角色列表，支持搜索和筛选",
    response_model=RoleListResponseModel,
)
@inject
async def list_roles(
    request: BaseRequest[ListRolesRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """查询角色列表"""
    list_roles_params = request.data
    result = await role_service.list_roles(
        tenant_id=list_roles_params.tenant_id,
        cursor=list_roles_params.cursor,
        limit=list_roles_params.limit,
        search=list_roles_params.search,
        status=list_roles_params.status
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取角色详情",
    description="获取指定角色的详细信息，包括权限列表",
    response_model=RoleResponseModel,
)
@inject
async def get_role_detail(
    request: BaseRequest[GetRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """获取角色详情"""
    get_role_params = request.data
    result = await role_service.get_role_detail(
        role_id=get_role_params.role_id,
        tenant_id=get_role_params.tenant_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新角色信息",
    description="更新角色的基本信息",
    response_model=RoleResponseModel,
)
@inject
async def update_role(
    request: BaseRequest[UpdateRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """更新角色信息"""
    update_role_params = request.data
    result = await role_service.update_role(
        role_id=update_role_params.role_id,
        tenant_id=update_role_params.tenant_id,
        role_name=update_role_params.role_name,
        description=update_role_params.description,
        status=update_role_params.status
    )
    return success_response(result, message="角色信息更新成功")


@router.post(
    "/delete",
    summary="删除角色",
    description="删除指定角色，支持软删除和强制删除",
    response_model=RoleOperationResponseModel,
)
@inject
async def delete_role(
    request: BaseRequest[DeleteRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """删除角色"""
    delete_role_params = request.data
    result = await role_service.delete_role(
        role_id=delete_role_params.role_id,
        tenant_id=delete_role_params.tenant_id,
        force=delete_role_params.force
    )
    return success_response(result, message="角色删除成功")


@router.post(
    "/assign_permissions",
    summary="分配权限",
    description="为角色分配一个或多个权限",
    response_model=RoleOperationResponseModel,
)
@inject
async def assign_permissions(
    request: BaseRequest[AssignPermissionsRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """分配权限"""
    assign_permissions_params = request.data
    result = await role_service.assign_permissions(
        role_id=assign_permissions_params.role_id,
        tenant_id=assign_permissions_params.tenant_id,
        permission_ids=assign_permissions_params.permission_ids
    )
    return success_response(result, message="权限分配成功")


@router.post(
    "/remove_permissions",
    summary="移除权限",
    description="移除角色的一个或多个权限",
    response_model=RoleOperationResponseModel,
)
@inject
async def remove_permissions(
    request: BaseRequest[RemovePermissionsRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service])
):
    """移除权限"""
    remove_permissions_params = request.data
    result = await role_service.remove_permissions(
        role_id=remove_permissions_params.role_id,
        tenant_id=remove_permissions_params.tenant_id,
        permission_ids=remove_permissions_params.permission_ids
    )
    return success_response(result, message="权限移除成功")



