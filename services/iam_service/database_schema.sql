-- IAM 服务数据库表设计
-- 兼容：PostgreSQL 14+ 和 MySQL 8.0+
-- 特点：不使用外键约束，通过应用层维护数据一致性
-- 支持多租户、软删除、审计字段
-- 更新日期：2024-01-01

-- ================================
-- 数据库兼容性说明
-- ================================
-- 1. 使用 JSON 而不是 JSONB（MySQL兼容）
-- 2. 使用 VARCHAR(45) 存储IP地址而不是 INET
-- 3. 主键使用 VARCHAR(64) 支持 UUID
-- 4. 时间戳使用标准 TIMESTAMP 格式

-- ================================
-- 1. 租户管理表
-- ================================

-- 租户表
CREATE TABLE tenants (
    tenant_id VARCHAR(64) PRIMARY KEY,
    tenant_name VARCHAR(255) NOT NULL,
    tenant_code VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    description TEXT,
    settings JSON DEFAULT '{}',
    max_users INTEGER DEFAULT 1000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 租户表索引
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_created_at ON tenants(created_at);

-- ================================
-- 2. 用户管理表
-- ================================

-- 用户表
CREATE TABLE users (
    user_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(320),
    phone VARCHAR(20),
    nickname VARCHAR(100),
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(64) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    profile JSON DEFAULT '{}',
    preferences JSON DEFAULT '{}',
    security_settings JSON DEFAULT '{}',
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 用户表索引和约束
CREATE UNIQUE INDEX uq_users_tenant_username ON users(tenant_id, username);
CREATE UNIQUE INDEX uq_users_tenant_email ON users(tenant_id, email) WHERE email IS NOT NULL;
CREATE INDEX idx_users_tenant_status ON users(tenant_id, status);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);

-- ================================
-- 3. 角色管理表
-- ================================

-- 角色表
CREATE TABLE roles (
    role_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    role_name VARCHAR(255) NOT NULL,
    role_code VARCHAR(100) NOT NULL,
    description TEXT,
    level INTEGER DEFAULT 1,
    parent_role_id VARCHAR(64),
    max_users INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    meta_data JSON DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 角色表索引和约束
CREATE UNIQUE INDEX uq_roles_tenant_code ON roles(tenant_id, role_code);
CREATE INDEX idx_roles_tenant_level ON roles(tenant_id, level);
CREATE INDEX idx_roles_parent ON roles(parent_role_id);
CREATE INDEX idx_roles_tenant_status ON roles(tenant_id, status);

-- ================================
-- 4. 权限管理表
-- ================================

-- 权限表
CREATE TABLE permissions (
    permission_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    permission_name VARCHAR(255) NOT NULL,
    permission_code VARCHAR(100) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    level INTEGER DEFAULT 1,
    parent_permission_id VARCHAR(64),
    is_inheritable BOOLEAN DEFAULT true,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    meta_data JSON DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 权限表索引和约束
CREATE UNIQUE INDEX uq_permissions_tenant_code ON permissions(tenant_id, permission_code);
CREATE INDEX idx_permissions_resource_action ON permissions(resource, action);
CREATE INDEX idx_permissions_tenant_level ON permissions(tenant_id, level);
CREATE INDEX idx_permissions_parent ON permissions(parent_permission_id);
CREATE INDEX idx_permissions_tenant_status ON permissions(tenant_id, status);

-- ================================
-- 5. 关联关系表
-- ================================

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    role_id VARCHAR(64) NOT NULL,
    assignment_type VARCHAR(20) DEFAULT 'permanent',
    effective_date DATE,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 用户角色关联表索引和约束
CREATE UNIQUE INDEX uq_user_roles_tenant_user_role ON user_roles(tenant_id, user_id, role_id);
CREATE INDEX idx_user_roles_tenant_user ON user_roles(tenant_id, user_id);
CREATE INDEX idx_user_roles_tenant_role ON user_roles(tenant_id, role_id);
CREATE INDEX idx_user_roles_status ON user_roles(status);
CREATE INDEX idx_user_roles_expiry ON user_roles(expiry_date);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    role_id VARCHAR(64) NOT NULL,
    permission_id VARCHAR(64) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 角色权限关联表索引和约束
CREATE UNIQUE INDEX uq_role_permissions_tenant_role_permission ON role_permissions(tenant_id, role_id, permission_id);
CREATE INDEX idx_role_permissions_tenant_role ON role_permissions(tenant_id, role_id);
CREATE INDEX idx_role_permissions_tenant_permission ON role_permissions(tenant_id, permission_id);
CREATE INDEX idx_role_permissions_status ON role_permissions(status);

-- 用户策略关联表
CREATE TABLE user_policies (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    policy_id VARCHAR(64) NOT NULL,
    assignment_type VARCHAR(20) DEFAULT 'permanent',
    effective_date DATE,
    expiry_date DATE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);

-- 用户策略关联表索引和约束
CREATE UNIQUE INDEX uq_user_policies_tenant_user_policy ON user_policies(tenant_id, user_id, policy_id);
CREATE INDEX idx_user_policies_tenant_user ON user_policies(tenant_id, user_id);
CREATE INDEX idx_user_policies_tenant_policy ON user_policies(tenant_id, policy_id);
CREATE INDEX idx_user_policies_status ON user_policies(status);
CREATE INDEX idx_user_policies_expiry ON user_policies(expiry_date);

-- ================================
-- 6. 认证与安全表
-- ================================

-- 用户会话历史表
CREATE TABLE user_session_history (
    session_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    device_info JSON DEFAULT '{}',
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_type VARCHAR(20) NOT NULL,
    last_activity_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    end_reason VARCHAR(50),
    duration_seconds INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户会话历史表索引
CREATE INDEX idx_user_session_history_tenant_user ON user_session_history(tenant_id, user_id);
CREATE INDEX idx_user_session_history_created_at ON user_session_history(created_at);
CREATE INDEX idx_user_session_history_user_created ON user_session_history(user_id, created_at);
CREATE INDEX idx_user_session_history_ip ON user_session_history(ip_address);
CREATE INDEX idx_user_session_history_end_reason ON user_session_history(end_reason);

-- 多因子认证表
CREATE TABLE user_mfa (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    mfa_type VARCHAR(20) NOT NULL,
    secret_key VARCHAR(255),
    backup_codes JSON,
    device_name VARCHAR(255),
    is_enabled BOOLEAN DEFAULT false,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 多因子认证表索引和约束
CREATE UNIQUE INDEX uq_user_mfa_tenant_user_type ON user_mfa(tenant_id, user_id, mfa_type);
CREATE INDEX idx_user_mfa_tenant_user ON user_mfa(tenant_id, user_id);
CREATE INDEX idx_user_mfa_type ON user_mfa(mfa_type);
CREATE INDEX idx_user_mfa_enabled ON user_mfa(is_enabled);

-- 验证码表
CREATE TABLE verification_codes (
    code_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64),
    code_type VARCHAR(20) NOT NULL,
    code_hash VARCHAR(255) NOT NULL,
    target VARCHAR(255) NOT NULL,
    scene VARCHAR(50) NOT NULL,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 5,
    is_verified BOOLEAN DEFAULT false,
    verified_at TIMESTAMP NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 验证码表索引
CREATE INDEX idx_verification_codes_tenant_user ON verification_codes(tenant_id, user_id);
CREATE INDEX idx_verification_codes_target ON verification_codes(target);
CREATE INDEX idx_verification_codes_type_scene ON verification_codes(code_type, scene);
CREATE INDEX idx_verification_codes_expires_at ON verification_codes(expires_at);
CREATE INDEX idx_verification_codes_verified ON verification_codes(is_verified);

-- 密码历史表
CREATE TABLE password_history (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 密码历史表索引
CREATE INDEX idx_password_history_tenant_user ON password_history(tenant_id, user_id);
CREATE INDEX idx_password_history_created_at ON password_history(created_at);
CREATE INDEX idx_password_history_user_created ON password_history(user_id, created_at);

-- ================================
-- 7. ABAC权限策略表
-- ================================

-- 权限策略表
CREATE TABLE permission_policies (
    policy_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    policy_name VARCHAR(255) NOT NULL,
    policy_code VARCHAR(100) NOT NULL,
    description TEXT,
    effect VARCHAR(10) NOT NULL,
    priority INTEGER DEFAULT 100,
    conditions JSON NOT NULL DEFAULT '[]',
    actions JSON NOT NULL DEFAULT '[]',
    resources JSON NOT NULL DEFAULT '[]',
    meta_data JSON DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 权限策略表索引和约束
CREATE UNIQUE INDEX uq_permission_policies_tenant_code ON permission_policies(tenant_id, policy_code);
CREATE INDEX idx_permission_policies_tenant_effect ON permission_policies(tenant_id, effect);
CREATE INDEX idx_permission_policies_priority ON permission_policies(priority);
CREATE INDEX idx_permission_policies_tenant_status ON permission_policies(tenant_id, status);

-- ================================
-- 8. 批量任务表
-- ================================

-- 批量任务表
CREATE TABLE batch_tasks (
    task_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    task_name VARCHAR(255) NOT NULL,
    task_type VARCHAR(100) NOT NULL,
    description TEXT,
    config JSON DEFAULT '{}',
    result JSON DEFAULT '{}',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    created_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 批量任务表索引
CREATE INDEX idx_batch_tasks_tenant_type ON batch_tasks(tenant_id, task_type);
CREATE INDEX idx_batch_tasks_status ON batch_tasks(status);
CREATE INDEX idx_batch_tasks_created_by ON batch_tasks(created_by);
CREATE INDEX idx_batch_tasks_created_at ON batch_tasks(created_at);
CREATE INDEX idx_batch_tasks_tenant_status ON batch_tasks(tenant_id, status);

-- ================================
-- 9. 审计日志表
-- ================================

-- 审计日志表
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    user_id VARCHAR(64),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(64),
    details JSON DEFAULT '{}',
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_context JSON DEFAULT '{}',
    result VARCHAR(20) NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 审计日志表索引
CREATE INDEX idx_audit_logs_tenant_user ON audit_logs(tenant_id, user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_result ON audit_logs(result);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_tenant_created ON audit_logs(tenant_id, created_at);
CREATE INDEX idx_audit_logs_user_created ON audit_logs(user_id, created_at);

-- ================================
-- 10. 系统配置表
-- ================================

-- 系统配置表
CREATE TABLE system_configs (
    config_id VARCHAR(64) PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL,
    config_value JSON NOT NULL,
    config_type VARCHAR(50) NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT false,
    version VARCHAR(20) NOT NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表索引和约束
CREATE UNIQUE INDEX uq_system_configs_config_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_type ON system_configs(config_type);
CREATE INDEX idx_system_configs_public ON system_configs(is_public);
CREATE INDEX idx_system_configs_created_at ON system_configs(created_at);

-- ================================
-- 11. 缓存管理表
-- ================================

-- 缓存键管理表
CREATE TABLE cache_keys (
    id BIGSERIAL PRIMARY KEY,
    cache_key VARCHAR(255) NOT NULL,
    cache_type VARCHAR(50) NOT NULL,
    description TEXT,
    ttl_seconds INTEGER,
    last_accessed TIMESTAMP NULL,
    access_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 缓存键管理表索引和约束
CREATE UNIQUE INDEX uq_cache_keys_cache_key ON cache_keys(cache_key);
CREATE INDEX idx_cache_keys_type ON cache_keys(cache_type);
CREATE INDEX idx_cache_keys_last_accessed ON cache_keys(last_accessed);
CREATE INDEX idx_cache_keys_created_at ON cache_keys(created_at);

-- ================================
-- 12. RAG 特定表（知识库和文档管理）
-- ================================

-- 知识库表
CREATE TABLE knowledge_bases (
    kb_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    kb_name VARCHAR(255) NOT NULL,
    kb_code VARCHAR(100) NOT NULL,
    description TEXT,
    config JSON DEFAULT '{}',
    embedding_model VARCHAR(100),
    vector_store VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    meta_data JSON DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 知识库表索引和约束
CREATE UNIQUE INDEX uq_knowledge_bases_tenant_code ON knowledge_bases(tenant_id, kb_code);
CREATE INDEX idx_knowledge_bases_tenant_status ON knowledge_bases(tenant_id, status);

-- 文档表
CREATE TABLE documents (
    doc_id VARCHAR(64) PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    kb_id VARCHAR(64) NOT NULL,
    title VARCHAR(255) NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    source_url VARCHAR(500),
    content_hash VARCHAR(64),
    chunk_count INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    meta_data JSON DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    created_by VARCHAR(64),
    updated_by VARCHAR(64)
);

-- 文档表索引
CREATE INDEX idx_documents_tenant_kb ON documents(tenant_id, kb_id);
CREATE INDEX idx_documents_tenant_status ON documents(tenant_id, status);
CREATE INDEX idx_documents_doc_type ON documents(doc_type);

-- 知识库访问权限表
CREATE TABLE knowledge_base_access (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL,
    kb_id VARCHAR(64) NOT NULL,
    principal_type VARCHAR(20) NOT NULL,
    principal_id VARCHAR(64) NOT NULL,
    access_level VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 知识库访问权限表索引和约束
CREATE UNIQUE INDEX uq_kb_access_tenant_kb_principal ON knowledge_base_access(tenant_id, kb_id, principal_type, principal_id);
CREATE INDEX idx_kb_access_tenant_kb ON knowledge_base_access(tenant_id, kb_id);
CREATE INDEX idx_kb_access_principal ON knowledge_base_access(principal_type, principal_id);
CREATE INDEX idx_kb_access_level ON knowledge_base_access(access_level);

-- ================================
-- 13. 数据库兼容性处理
-- ================================

-- PostgreSQL 特定的UUID函数（如果使用PostgreSQL）
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- PostgreSQL 兼容性：使用触发器更新 updated_at 字段
-- 创建通用的 updated_at 触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有需要自动更新 updated_at 的表创建触发器
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_role_permissions_updated_at BEFORE UPDATE ON role_permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_policies_updated_at BEFORE UPDATE ON user_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_session_history_updated_at BEFORE UPDATE ON user_session_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_mfa_updated_at BEFORE UPDATE ON user_mfa FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_verification_codes_updated_at BEFORE UPDATE ON verification_codes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_password_history_updated_at BEFORE UPDATE ON password_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_permission_policies_updated_at BEFORE UPDATE ON permission_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_batch_tasks_updated_at BEFORE UPDATE ON batch_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cache_keys_updated_at BEFORE UPDATE ON cache_keys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_knowledge_bases_updated_at BEFORE UPDATE ON knowledge_bases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_knowledge_base_access_updated_at BEFORE UPDATE ON knowledge_base_access FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- 13. 初始化数据
-- ================================

-- 插入默认系统配置
INSERT INTO system_configs (config_id, config_key, config_value, config_type, description, is_public, version, created_by) VALUES
('sys_001', 'password_policy', '{"min_length": 8, "require_uppercase": true, "require_lowercase": true, "require_number": true, "require_special_char": true, "max_age_days": 90, "history_count": 5}', 'json', '默认密码策略', false, '1.0', 'system'),
('sys_002', 'session_config', '{"access_token_ttl": 7200, "refresh_token_ttl": 86400, "max_sessions_per_user": 5, "session_timeout": 1800}', 'json', '会话配置', false, '1.0', 'system'),
('sys_003', 'mfa_config', '{"totp_issuer": "IAM Service", "backup_codes_count": 10, "code_length": 6}', 'json', 'MFA配置', false, '1.0', 'system'),
('sys_004', 'verification_config', '{"sms_ttl": 300, "email_ttl": 1800, "max_attempts": 5, "resend_interval": 60}', 'json', '验证码配置', false, '1.0', 'system'),
('sys_005', 'cache_config', '{"permissions_ttl": 3600, "roles_ttl": 7200, "policies_ttl": 1800, "user_info_ttl": 1800}', 'json', '缓存配置', false, '1.0', 'system'),
('sys_006', 'audit_config', '{"log_retention_days": 90, "sensitive_fields": ["password", "secret_key", "token"], "log_level": "info"}', 'json', '审计配置', false, '1.0', 'system'),
('sys_007', 'rate_limit_config', '{"login_per_minute": 5, "api_per_second": 100, "batch_per_hour": 10}', 'json', '限流配置', false, '1.0', 'system');

-- ================================
-- 14. 数据库维护和优化
-- ================================

-- 清理过期验证码的存储过程（PostgreSQL版本）
-- CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
-- RETURNS INTEGER AS $$
-- DECLARE
--     deleted_count INTEGER;
-- BEGIN
--     DELETE FROM verification_codes WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '1 day';
--     GET DIAGNOSTICS deleted_count = ROW_COUNT;
--     RETURN deleted_count;
-- END;
-- $$ LANGUAGE plpgsql;

-- 清理旧审计日志的存储过程（保留90天）
-- CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
-- RETURNS INTEGER AS $$
-- DECLARE
--     deleted_count INTEGER;
-- BEGIN
--     DELETE FROM audit_logs WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
--     GET DIAGNOSTICS deleted_count = ROW_COUNT;
--     RETURN deleted_count;
-- END;
-- $$ LANGUAGE plpgsql;

-- 清理旧密码历史（保留最近5个）
-- CREATE OR REPLACE FUNCTION cleanup_old_password_history()
-- RETURNS INTEGER AS $$
-- DECLARE
--     deleted_count INTEGER;
-- BEGIN
--     WITH ranked_passwords AS (
--         SELECT id, ROW_NUMBER() OVER (PARTITION BY tenant_id, user_id ORDER BY created_at DESC) as rn
--         FROM password_history
--     )
--     DELETE FROM password_history WHERE id IN (
--         SELECT id FROM ranked_passwords WHERE rn > 5
--     );
--     GET DIAGNOSTICS deleted_count = ROW_COUNT;
--     RETURN deleted_count;
-- END;
-- $$ LANGUAGE plpgsql;

-- ================================
-- 15. 数据库设计说明文档
-- ================================

/*
IAM 服务数据库设计说明（更新版）

1. 设计原则：
   - 兼容 PostgreSQL 14+ 和 MySQL 8.0+
   - 不使用外键约束，通过应用层维护数据一致性
   - 支持多租户架构，所有业务表都包含 tenant_id
   - 支持软删除，使用 deleted_at 字段
   - 统一的审计字段：created_at, updated_at, created_by, updated_by
   - 使用 JSON 存储灵活的配置和元数据（兼容两种数据库）

2. 数据库兼容性：
   - 使用 JSON 而不是 JSONB（MySQL 兼容）
   - 使用 VARCHAR(45) 存储 IP 地址而不是 INET
   - 使用 BIGSERIAL 自增主键（PostgreSQL 原生）
   - 时间戳使用标准 TIMESTAMP 格式
   - 主键使用 VARCHAR(64) 支持 UUID 和自定义 ID
   - 使用触发器实现 updated_at 字段自动更新

3. 模型结构：
   - 核心模型：Tenant, User, Role, Permission
   - 关联模型：UserRole, RolePermission, UserPolicy
   - 认证模型：UserSessionHistory, UserMFA, VerificationCode, PasswordHistory
   - 策略模型：PermissionPolicy
   - 系统模型：SystemConfig, CacheKey, BatchTask
   - 审计模型：AuditLog

4. 安全特性：
   - 密码使用 hash+salt 存储
   - 支持密码历史记录防止重用
   - 多因子认证支持
   - 完整的审计日志
   - 会话管理（建议活跃会话存储在 Redis）

5. 性能优化：
   - 合理的索引设计，包括复合索引
   - 软删除使用部分索引（WHERE deleted_at IS NULL）
   - JSON 字段用于存储半结构化数据
   - 支持分区表（大数据量场景）

6. RAG 项目特性：
   - 支持基于属性的访问控制（ABAC）
   - 灵活的权限策略配置
   - 支持文档和知识库的权限管理
   - 可扩展的元数据存储

7. 运维支持：
   - 系统配置表支持动态配置
   - 缓存管理表支持缓存失效
   - 批量任务跟踪
   - 完整的审计追踪

8. 数据维护：
   - 定期清理过期数据
   - 监控表和索引大小
   - 定期更新统计信息
   - 监控慢查询日志

9. 扩展性：
   - JSON 字段支持灵活的配置扩展
   - 元数据字段支持业务扩展
   - 策略表支持复杂的权限控制
   - 支持多租户水平扩展

10. 最佳实践：
    - 使用事务确保数据一致性
    - 实施软删除而非物理删除
    - 定期备份和恢复测试
    - 监控数据库性能指标
    - 实施数据保留策略
*/


