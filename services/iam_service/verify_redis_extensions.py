#!/usr/bin/env python3
"""
Redis仓库扩展验证脚本

验证所有新添加的Redis方法是否正确实现
"""

import sys
import inspect
from typing import get_type_hints

# 添加路径以便导入
sys.path.insert(0, '../../')

try:
    from commonlib.storages.persistence.redis.repository import RedisRepository
    print("✅ Redis仓库导入成功")
except ImportError as e:
    print(f"❌ Redis仓库导入失败: {e}")
    print("尝试直接验证方法存在性...")

    # 如果导入失败，尝试直接检查文件内容
    import os
    repo_file = "../../commonlib/storages/persistence/redis/repository.py"
    if os.path.exists(repo_file):
        with open(repo_file, 'r') as f:
            content = f.read()

        new_methods = ['keys', 'expire', 'ttl', 'incr', 'decr', 'rpush', 'lpop', 'llen', 'ltrim', 'sismember', 'scard', 'mget', 'mset', 'ping', 'info']

        print("\n通过文件内容验证方法存在性:")
        for method in new_methods:
            if f"async def {method}(" in content:
                print(f"✅ {method} 方法已添加")
            else:
                print(f"❌ {method} 方法缺失")

        print("\n✅ 文件内容验证完成")
        sys.exit(0)
    else:
        print(f"❌ 找不到文件: {repo_file}")
        sys.exit(1)


def verify_method_exists(cls, method_name, expected_signature=None):
    """验证方法是否存在并检查签名"""
    if not hasattr(cls, method_name):
        print(f"❌ 方法 {method_name} 不存在")
        return False
    
    method = getattr(cls, method_name)
    if not callable(method):
        print(f"❌ {method_name} 不是可调用方法")
        return False
    
    # 检查是否是异步方法
    if not inspect.iscoroutinefunction(method):
        print(f"❌ {method_name} 不是异步方法")
        return False
    
    print(f"✅ {method_name} 方法存在且为异步方法")
    
    # 如果提供了期望的签名，进行检查
    if expected_signature:
        try:
            sig = inspect.signature(method)
            print(f"   签名: {sig}")
        except Exception as e:
            print(f"   ⚠️  无法获取签名: {e}")
    
    return True


def main():
    """主验证函数"""
    print("Redis仓库扩展方法验证")
    print("=" * 50)
    
    # 定义需要验证的方法及其期望签名
    methods_to_verify = [
        # 基础键操作
        ("keys", "获取匹配模式的所有键"),
        ("expire", "设置键的过期时间"),
        ("ttl", "获取键的剩余生存时间"),
        ("incr", "递增键的值"),
        ("decr", "递减键的值"),
        
        # 列表操作
        ("rpush", "从右侧推入列表"),
        ("lpop", "从左侧弹出列表元素"),
        ("llen", "获取列表长度"),
        ("ltrim", "修剪列表到指定范围"),
        
        # 集合操作
        ("sismember", "检查成员是否在集合中"),
        ("scard", "获取集合大小"),
        
        # 批量操作
        ("mget", "批量获取多个键的值"),
        ("mset", "批量设置多个键值对"),
        ("delete_pattern", "删除匹配模式的所有键"),
        
        # 数据库操作
        ("flushdb", "清空当前数据库"),
        ("ping", "测试连接"),
        ("info", "获取Redis服务器信息"),
        
        # 事务操作
        ("watch", "监视键的变化"),
        ("unwatch", "取消监视所有键"),
        ("multi", "开始事务"),
        ("execute", "执行事务"),
        
        # 发布订阅
        ("publish", "发布消息到频道"),
        ("subscribe", "订阅频道"),
        
        # 工具方法
        ("clear_all", "清理所有带前缀的键"),
    ]
    
    success_count = 0
    total_count = len(methods_to_verify)
    
    print(f"\n验证 {total_count} 个新增方法:")
    print("-" * 30)
    
    for method_name, description in methods_to_verify:
        print(f"\n{method_name}: {description}")
        if verify_method_exists(RedisRepository, method_name):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"验证结果: {success_count}/{total_count} 方法通过验证")
    
    if success_count == total_count:
        print("🎉 所有新增方法都已正确实现！")
        
        # 额外验证：检查原有方法是否仍然存在
        print("\n验证原有方法兼容性:")
        original_methods = ["get", "set", "delete", "exists", "lpush", "rpop", "lrange", "sadd", "srem", "smembers"]
        
        for method in original_methods:
            if hasattr(RedisRepository, method):
                print(f"✅ 原有方法 {method} 保持兼容")
            else:
                print(f"❌ 原有方法 {method} 丢失")
        
        print("\n📋 新增方法分类统计:")
        print("   🔑 基础键操作: 5个")
        print("   📝 列表操作: 4个")
        print("   🎯 集合操作: 2个")
        print("   📦 批量操作: 3个")
        print("   🔧 数据库操作: 3个")
        print("   🔄 事务操作: 4个")
        print("   📢 发布订阅: 2个")
        print("   🛠️ 工具方法: 1个")
        print(f"   📊 总计: {total_count}个新方法")
        
        return True
    else:
        print(f"❌ 有 {total_count - success_count} 个方法验证失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
