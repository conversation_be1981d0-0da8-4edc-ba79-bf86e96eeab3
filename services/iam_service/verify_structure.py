#!/usr/bin/env python3
"""
IAM 服务目录结构验证脚本

验证新的目录结构是否完整和正确
"""

import os
import sys
from pathlib import Path


def check_file_exists(file_path: str, description: str) -> bool:
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (缺失)")
        return False


def check_directory_exists(dir_path: str, description: str) -> bool:
    """检查目录是否存在"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (缺失)")
        return False


def main():
    """主验证函数"""
    print("🔍 IAM 服务目录结构验证")
    print("=" * 50)
    
    base_path = Path(__file__).parent
    os.chdir(base_path)
    
    checks = []
    
    # 核心文件检查
    print("\n📁 核心文件检查:")
    checks.append(check_file_exists("main.py", "应用入口"))
    checks.append(check_file_exists("container.py", "DI 容器"))
    checks.append(check_file_exists("configs/config.json", "配置文件"))
    checks.append(check_file_exists("requirements.txt", "依赖文件"))
    checks.append(check_file_exists("Dockerfile", "容器配置"))
    checks.append(check_file_exists("start-server.sh", "启动脚本"))
    checks.append(check_file_exists("README.md", "说明文档"))
    
    # 文档文件检查
    print("\n📄 文档文件检查:")
    checks.append(check_file_exists("api_doc.md", "API 文档"))
    checks.append(check_file_exists("database_schema.sql", "数据库架构"))
    
    # 目录结构检查
    print("\n📂 目录结构检查:")
    checks.append(check_directory_exists("routes", "路由目录"))
    checks.append(check_directory_exists("services", "服务目录"))
    checks.append(check_directory_exists("tasks", "任务目录"))
    checks.append(check_directory_exists("tests", "测试目录"))
    
    # 路由文件检查
    print("\n🛣️ 路由文件检查:")
    route_files = [
        "routes/__init__.py",
        "routes/health.py",
        "routes/tenants.py", 
        "routes/users.py",
        "routes/auth.py",
        "routes/roles.py",
        "routes/permissions.py",
        "routes/knowledge_bases.py",
        "routes/documents.py",
        "routes/system.py"
    ]
    
    for route_file in route_files:
        checks.append(check_file_exists(route_file, f"路由文件"))
    
    # 服务文件检查
    print("\n🔧 服务文件检查:")
    service_files = [
        "services/__init__.py",
        "services/tenant_service.py",
        "services/user_service.py",
        "services/auth_service.py",
        "services/role_service.py",
        "services/permission_service.py",
        "services/knowledge_base_service.py",
        "services/document_service.py"
    ]
    
    for service_file in service_files:
        checks.append(check_file_exists(service_file, f"服务文件"))
    
    # 任务文件检查
    print("\n⚡ 任务文件检查:")
    task_files = [
        "tasks/__init__.py",
        "tasks/scheduler.py",
        "tasks/user_tasks.py",
        "tasks/cleanup_tasks.py",
        "tasks/notification_tasks.py"
    ]
    
    for task_file in task_files:
        checks.append(check_file_exists(task_file, f"任务文件"))
    
    # 测试文件检查
    print("\n🧪 测试文件检查:")
    test_files = [
        "tests/__init__.py",
        "tests/conftest.py",
        "tests/test_routes/__init__.py",
        "tests/test_routes/test_health.py",
        "tests/test_services/__init__.py",
        "tests/test_services/test_tenant_service.py"
    ]
    
    for test_file in test_files:
        checks.append(check_file_exists(test_file, f"测试文件"))
    
    # 部署文件检查
    print("\n🚀 部署文件检查:")
    checks.append(check_file_exists("docker-compose.yml", "Docker Compose"))
    checks.append(check_file_exists("Makefile", "Makefile"))
    checks.append(check_file_exists(".gitignore", "Git 忽略文件"))
    
    # 检查旧目录是否已清理
    print("\n🧹 旧目录清理检查:")
    old_paths = ["backend"]
    for old_path in old_paths:
        if os.path.exists(old_path):
            print(f"⚠️  旧目录仍存在: {old_path}")
            checks.append(False)
        else:
            print(f"✅ 旧目录已清理: {old_path}")
            checks.append(True)
    
    # 总结
    print("\n" + "=" * 50)
    passed = sum(checks)
    total = len(checks)
    
    if passed == total:
        print(f"🎉 验证通过! ({passed}/{total})")
        print("✅ IAM 服务目录结构重构完成!")
        return 0
    else:
        print(f"❌ 验证失败! ({passed}/{total})")
        print("请检查缺失的文件和目录")
        return 1


if __name__ == "__main__":
    sys.exit(main())
